[0.014s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu -DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu
[0.032s] -- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
[0.280s] -- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)
[0.314s] -- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
[0.325s] -- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
[0.340s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.356s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.432s] -- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
[0.434s] -- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
[0.516s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.596s] -- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)
[0.612s] -- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)
[0.630s] -- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)
[0.665s] -- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)
[0.694s] -- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)
[0.698s] -- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)
[0.839s] -- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)
[0.844s] -- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)
[0.846s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.848s] -- Found sl_vcu_all: 0.0.1 (/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake)
[0.911s] -- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
[0.912s] -- Found ament_cmake_gtest: 2.5.4 (/opt/ros/jazzy/share/ament_cmake_gtest/cmake)
[1.017s] -- Added test 'copyright' to check source files copyright and LICENSE
[1.020s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.021s] -- Configured cppcheck include dirs: /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include
[1.021s] -- Configured cppcheck exclude dirs and/or files: 
[1.024s] -- Added test 'cpplint' to check C / C++ code against the Google style
[1.025s] -- Configured cpplint exclude dirs and/or files: 
[1.025s] -- Added test 'flake8' to check Python code syntax and style conventions
[1.025s] -- Configured 'flake8' exclude dirs and/or files: 
[1.026s] -- Added test 'lint_cmake' to check CMake code style
[1.027s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.030s] -- Added test 'uncrustify' to check C / C++ code style
[1.030s] -- Configured uncrustify additional arguments: 
[1.031s] -- Added test 'xmllint' to check XML markup files
[1.032s] -- Configuring done (1.0s)
[1.089s] -- Generating done (0.0s)
[1.099s] -- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu
[1.110s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu' returned '0': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu -DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu
[1.112s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
[1.176s] [  5%] [32mBuilding CXX object CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o[0m
