# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu

# Include any dependencies generated for this target.
include CMakeFiles/sl_pmu_driver.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/sl_pmu_driver.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/sl_pmu_driver.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/sl_pmu_driver.dir/flags.make

CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.o: CMakeFiles/sl_pmu_driver.dir/flags.make
CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.o: /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/driver/socket_can_driver.cpp
CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.o: CMakeFiles/sl_pmu_driver.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.o -MF CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.o.d -o CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.o -c /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/driver/socket_can_driver.cpp

CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/driver/socket_can_driver.cpp > CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.i

CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/driver/socket_can_driver.cpp -o CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.s

CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.o: CMakeFiles/sl_pmu_driver.dir/flags.make
CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.o: /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/driver/communication_driver_factory.cpp
CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.o: CMakeFiles/sl_pmu_driver.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.o -MF CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.o.d -o CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.o -c /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/driver/communication_driver_factory.cpp

CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/driver/communication_driver_factory.cpp > CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.i

CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/driver/communication_driver_factory.cpp -o CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.s

# Object files for target sl_pmu_driver
sl_pmu_driver_OBJECTS = \
"CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.o" \
"CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.o"

# External object files for target sl_pmu_driver
sl_pmu_driver_EXTERNAL_OBJECTS =

libsl_pmu_driver.a: CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.o
libsl_pmu_driver.a: CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.o
libsl_pmu_driver.a: CMakeFiles/sl_pmu_driver.dir/build.make
libsl_pmu_driver.a: CMakeFiles/sl_pmu_driver.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library libsl_pmu_driver.a"
	$(CMAKE_COMMAND) -P CMakeFiles/sl_pmu_driver.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sl_pmu_driver.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/sl_pmu_driver.dir/build: libsl_pmu_driver.a
.PHONY : CMakeFiles/sl_pmu_driver.dir/build

CMakeFiles/sl_pmu_driver.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/sl_pmu_driver.dir/cmake_clean.cmake
.PHONY : CMakeFiles/sl_pmu_driver.dir/clean

CMakeFiles/sl_pmu_driver.dir/depend:
	cd /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu/CMakeFiles/sl_pmu_driver.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/sl_pmu_driver.dir/depend

