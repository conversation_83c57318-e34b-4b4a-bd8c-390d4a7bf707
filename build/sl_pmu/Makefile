# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu/CMakeFiles /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named uninstall

# Build rule for target.
uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uninstall
.PHONY : uninstall

# fast build rule for target.
uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/build
.PHONY : uninstall/fast

#=============================================================================
# Target rules for targets named sl_pmu_uninstall

# Build rule for target.
sl_pmu_uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sl_pmu_uninstall
.PHONY : sl_pmu_uninstall

# fast build rule for target.
sl_pmu_uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_uninstall.dir/build.make CMakeFiles/sl_pmu_uninstall.dir/build
.PHONY : sl_pmu_uninstall/fast

#=============================================================================
# Target rules for targets named sl_pmu_common

# Build rule for target.
sl_pmu_common: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sl_pmu_common
.PHONY : sl_pmu_common

# fast build rule for target.
sl_pmu_common/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_common.dir/build.make CMakeFiles/sl_pmu_common.dir/build
.PHONY : sl_pmu_common/fast

#=============================================================================
# Target rules for targets named sl_pmu_driver

# Build rule for target.
sl_pmu_driver: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sl_pmu_driver
.PHONY : sl_pmu_driver

# fast build rule for target.
sl_pmu_driver/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_driver.dir/build.make CMakeFiles/sl_pmu_driver.dir/build
.PHONY : sl_pmu_driver/fast

#=============================================================================
# Target rules for targets named sl_pmu_protocol

# Build rule for target.
sl_pmu_protocol: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sl_pmu_protocol
.PHONY : sl_pmu_protocol

# fast build rule for target.
sl_pmu_protocol/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_protocol.dir/build.make CMakeFiles/sl_pmu_protocol.dir/build
.PHONY : sl_pmu_protocol/fast

#=============================================================================
# Target rules for targets named sl_pmu_device

# Build rule for target.
sl_pmu_device: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sl_pmu_device
.PHONY : sl_pmu_device

# fast build rule for target.
sl_pmu_device/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_device.dir/build.make CMakeFiles/sl_pmu_device.dir/build
.PHONY : sl_pmu_device/fast

#=============================================================================
# Target rules for targets named sl_pmu_application

# Build rule for target.
sl_pmu_application: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sl_pmu_application
.PHONY : sl_pmu_application

# fast build rule for target.
sl_pmu_application/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_application.dir/build.make CMakeFiles/sl_pmu_application.dir/build
.PHONY : sl_pmu_application/fast

#=============================================================================
# Target rules for targets named sl_pmu_ros_node

# Build rule for target.
sl_pmu_ros_node: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sl_pmu_ros_node
.PHONY : sl_pmu_ros_node

# fast build rule for target.
sl_pmu_ros_node/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_ros_node.dir/build.make CMakeFiles/sl_pmu_ros_node.dir/build
.PHONY : sl_pmu_ros_node/fast

#=============================================================================
# Target rules for targets named pmu_node

# Build rule for target.
pmu_node: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 pmu_node
.PHONY : pmu_node

# fast build rule for target.
pmu_node/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pmu_node.dir/build.make CMakeFiles/pmu_node.dir/build
.PHONY : pmu_node/fast

src/application/pmu_application.o: src/application/pmu_application.cpp.o
.PHONY : src/application/pmu_application.o

# target to build an object file
src/application/pmu_application.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_application.dir/build.make CMakeFiles/sl_pmu_application.dir/src/application/pmu_application.cpp.o
.PHONY : src/application/pmu_application.cpp.o

src/application/pmu_application.i: src/application/pmu_application.cpp.i
.PHONY : src/application/pmu_application.i

# target to preprocess a source file
src/application/pmu_application.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_application.dir/build.make CMakeFiles/sl_pmu_application.dir/src/application/pmu_application.cpp.i
.PHONY : src/application/pmu_application.cpp.i

src/application/pmu_application.s: src/application/pmu_application.cpp.s
.PHONY : src/application/pmu_application.s

# target to generate assembly for a file
src/application/pmu_application.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_application.dir/build.make CMakeFiles/sl_pmu_application.dir/src/application/pmu_application.cpp.s
.PHONY : src/application/pmu_application.cpp.s

src/common/config_manager.o: src/common/config_manager.cpp.o
.PHONY : src/common/config_manager.o

# target to build an object file
src/common/config_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_common.dir/build.make CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o
.PHONY : src/common/config_manager.cpp.o

src/common/config_manager.i: src/common/config_manager.cpp.i
.PHONY : src/common/config_manager.i

# target to preprocess a source file
src/common/config_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_common.dir/build.make CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.i
.PHONY : src/common/config_manager.cpp.i

src/common/config_manager.s: src/common/config_manager.cpp.s
.PHONY : src/common/config_manager.s

# target to generate assembly for a file
src/common/config_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_common.dir/build.make CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.s
.PHONY : src/common/config_manager.cpp.s

src/device/motor_controller.o: src/device/motor_controller.cpp.o
.PHONY : src/device/motor_controller.o

# target to build an object file
src/device/motor_controller.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_device.dir/build.make CMakeFiles/sl_pmu_device.dir/src/device/motor_controller.cpp.o
.PHONY : src/device/motor_controller.cpp.o

src/device/motor_controller.i: src/device/motor_controller.cpp.i
.PHONY : src/device/motor_controller.i

# target to preprocess a source file
src/device/motor_controller.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_device.dir/build.make CMakeFiles/sl_pmu_device.dir/src/device/motor_controller.cpp.i
.PHONY : src/device/motor_controller.cpp.i

src/device/motor_controller.s: src/device/motor_controller.cpp.s
.PHONY : src/device/motor_controller.s

# target to generate assembly for a file
src/device/motor_controller.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_device.dir/build.make CMakeFiles/sl_pmu_device.dir/src/device/motor_controller.cpp.s
.PHONY : src/device/motor_controller.cpp.s

src/device/power_controller.o: src/device/power_controller.cpp.o
.PHONY : src/device/power_controller.o

# target to build an object file
src/device/power_controller.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_device.dir/build.make CMakeFiles/sl_pmu_device.dir/src/device/power_controller.cpp.o
.PHONY : src/device/power_controller.cpp.o

src/device/power_controller.i: src/device/power_controller.cpp.i
.PHONY : src/device/power_controller.i

# target to preprocess a source file
src/device/power_controller.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_device.dir/build.make CMakeFiles/sl_pmu_device.dir/src/device/power_controller.cpp.i
.PHONY : src/device/power_controller.cpp.i

src/device/power_controller.s: src/device/power_controller.cpp.s
.PHONY : src/device/power_controller.s

# target to generate assembly for a file
src/device/power_controller.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_device.dir/build.make CMakeFiles/sl_pmu_device.dir/src/device/power_controller.cpp.s
.PHONY : src/device/power_controller.cpp.s

src/driver/communication_driver_factory.o: src/driver/communication_driver_factory.cpp.o
.PHONY : src/driver/communication_driver_factory.o

# target to build an object file
src/driver/communication_driver_factory.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_driver.dir/build.make CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.o
.PHONY : src/driver/communication_driver_factory.cpp.o

src/driver/communication_driver_factory.i: src/driver/communication_driver_factory.cpp.i
.PHONY : src/driver/communication_driver_factory.i

# target to preprocess a source file
src/driver/communication_driver_factory.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_driver.dir/build.make CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.i
.PHONY : src/driver/communication_driver_factory.cpp.i

src/driver/communication_driver_factory.s: src/driver/communication_driver_factory.cpp.s
.PHONY : src/driver/communication_driver_factory.s

# target to generate assembly for a file
src/driver/communication_driver_factory.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_driver.dir/build.make CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.s
.PHONY : src/driver/communication_driver_factory.cpp.s

src/driver/socket_can_driver.o: src/driver/socket_can_driver.cpp.o
.PHONY : src/driver/socket_can_driver.o

# target to build an object file
src/driver/socket_can_driver.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_driver.dir/build.make CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.o
.PHONY : src/driver/socket_can_driver.cpp.o

src/driver/socket_can_driver.i: src/driver/socket_can_driver.cpp.i
.PHONY : src/driver/socket_can_driver.i

# target to preprocess a source file
src/driver/socket_can_driver.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_driver.dir/build.make CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.i
.PHONY : src/driver/socket_can_driver.cpp.i

src/driver/socket_can_driver.s: src/driver/socket_can_driver.cpp.s
.PHONY : src/driver/socket_can_driver.s

# target to generate assembly for a file
src/driver/socket_can_driver.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_driver.dir/build.make CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.s
.PHONY : src/driver/socket_can_driver.cpp.s

src/protocol/canopen_sdo_protocol.o: src/protocol/canopen_sdo_protocol.cpp.o
.PHONY : src/protocol/canopen_sdo_protocol.o

# target to build an object file
src/protocol/canopen_sdo_protocol.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_protocol.dir/build.make CMakeFiles/sl_pmu_protocol.dir/src/protocol/canopen_sdo_protocol.cpp.o
.PHONY : src/protocol/canopen_sdo_protocol.cpp.o

src/protocol/canopen_sdo_protocol.i: src/protocol/canopen_sdo_protocol.cpp.i
.PHONY : src/protocol/canopen_sdo_protocol.i

# target to preprocess a source file
src/protocol/canopen_sdo_protocol.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_protocol.dir/build.make CMakeFiles/sl_pmu_protocol.dir/src/protocol/canopen_sdo_protocol.cpp.i
.PHONY : src/protocol/canopen_sdo_protocol.cpp.i

src/protocol/canopen_sdo_protocol.s: src/protocol/canopen_sdo_protocol.cpp.s
.PHONY : src/protocol/canopen_sdo_protocol.s

# target to generate assembly for a file
src/protocol/canopen_sdo_protocol.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_protocol.dir/build.make CMakeFiles/sl_pmu_protocol.dir/src/protocol/canopen_sdo_protocol.cpp.s
.PHONY : src/protocol/canopen_sdo_protocol.cpp.s

src/protocol/communication_protocol_factory.o: src/protocol/communication_protocol_factory.cpp.o
.PHONY : src/protocol/communication_protocol_factory.o

# target to build an object file
src/protocol/communication_protocol_factory.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_protocol.dir/build.make CMakeFiles/sl_pmu_protocol.dir/src/protocol/communication_protocol_factory.cpp.o
.PHONY : src/protocol/communication_protocol_factory.cpp.o

src/protocol/communication_protocol_factory.i: src/protocol/communication_protocol_factory.cpp.i
.PHONY : src/protocol/communication_protocol_factory.i

# target to preprocess a source file
src/protocol/communication_protocol_factory.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_protocol.dir/build.make CMakeFiles/sl_pmu_protocol.dir/src/protocol/communication_protocol_factory.cpp.i
.PHONY : src/protocol/communication_protocol_factory.cpp.i

src/protocol/communication_protocol_factory.s: src/protocol/communication_protocol_factory.cpp.s
.PHONY : src/protocol/communication_protocol_factory.s

# target to generate assembly for a file
src/protocol/communication_protocol_factory.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_protocol.dir/build.make CMakeFiles/sl_pmu_protocol.dir/src/protocol/communication_protocol_factory.cpp.s
.PHONY : src/protocol/communication_protocol_factory.cpp.s

src/protocol/modbus_rtu_protocol.o: src/protocol/modbus_rtu_protocol.cpp.o
.PHONY : src/protocol/modbus_rtu_protocol.o

# target to build an object file
src/protocol/modbus_rtu_protocol.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_protocol.dir/build.make CMakeFiles/sl_pmu_protocol.dir/src/protocol/modbus_rtu_protocol.cpp.o
.PHONY : src/protocol/modbus_rtu_protocol.cpp.o

src/protocol/modbus_rtu_protocol.i: src/protocol/modbus_rtu_protocol.cpp.i
.PHONY : src/protocol/modbus_rtu_protocol.i

# target to preprocess a source file
src/protocol/modbus_rtu_protocol.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_protocol.dir/build.make CMakeFiles/sl_pmu_protocol.dir/src/protocol/modbus_rtu_protocol.cpp.i
.PHONY : src/protocol/modbus_rtu_protocol.cpp.i

src/protocol/modbus_rtu_protocol.s: src/protocol/modbus_rtu_protocol.cpp.s
.PHONY : src/protocol/modbus_rtu_protocol.s

# target to generate assembly for a file
src/protocol/modbus_rtu_protocol.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_protocol.dir/build.make CMakeFiles/sl_pmu_protocol.dir/src/protocol/modbus_rtu_protocol.cpp.s
.PHONY : src/protocol/modbus_rtu_protocol.cpp.s

src/ros_node/pmu_main.o: src/ros_node/pmu_main.cpp.o
.PHONY : src/ros_node/pmu_main.o

# target to build an object file
src/ros_node/pmu_main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pmu_node.dir/build.make CMakeFiles/pmu_node.dir/src/ros_node/pmu_main.cpp.o
.PHONY : src/ros_node/pmu_main.cpp.o

src/ros_node/pmu_main.i: src/ros_node/pmu_main.cpp.i
.PHONY : src/ros_node/pmu_main.i

# target to preprocess a source file
src/ros_node/pmu_main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pmu_node.dir/build.make CMakeFiles/pmu_node.dir/src/ros_node/pmu_main.cpp.i
.PHONY : src/ros_node/pmu_main.cpp.i

src/ros_node/pmu_main.s: src/ros_node/pmu_main.cpp.s
.PHONY : src/ros_node/pmu_main.s

# target to generate assembly for a file
src/ros_node/pmu_main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pmu_node.dir/build.make CMakeFiles/pmu_node.dir/src/ros_node/pmu_main.cpp.s
.PHONY : src/ros_node/pmu_main.cpp.s

src/ros_node/pmu_ros_node.o: src/ros_node/pmu_ros_node.cpp.o
.PHONY : src/ros_node/pmu_ros_node.o

# target to build an object file
src/ros_node/pmu_ros_node.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_ros_node.dir/build.make CMakeFiles/sl_pmu_ros_node.dir/src/ros_node/pmu_ros_node.cpp.o
.PHONY : src/ros_node/pmu_ros_node.cpp.o

src/ros_node/pmu_ros_node.i: src/ros_node/pmu_ros_node.cpp.i
.PHONY : src/ros_node/pmu_ros_node.i

# target to preprocess a source file
src/ros_node/pmu_ros_node.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_ros_node.dir/build.make CMakeFiles/sl_pmu_ros_node.dir/src/ros_node/pmu_ros_node.cpp.i
.PHONY : src/ros_node/pmu_ros_node.cpp.i

src/ros_node/pmu_ros_node.s: src/ros_node/pmu_ros_node.cpp.s
.PHONY : src/ros_node/pmu_ros_node.s

# target to generate assembly for a file
src/ros_node/pmu_ros_node.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sl_pmu_ros_node.dir/build.make CMakeFiles/sl_pmu_ros_node.dir/src/ros_node/pmu_ros_node.cpp.s
.PHONY : src/ros_node/pmu_ros_node.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... sl_pmu_uninstall"
	@echo "... uninstall"
	@echo "... pmu_node"
	@echo "... sl_pmu_application"
	@echo "... sl_pmu_common"
	@echo "... sl_pmu_device"
	@echo "... sl_pmu_driver"
	@echo "... sl_pmu_protocol"
	@echo "... sl_pmu_ros_node"
	@echo "... src/application/pmu_application.o"
	@echo "... src/application/pmu_application.i"
	@echo "... src/application/pmu_application.s"
	@echo "... src/common/config_manager.o"
	@echo "... src/common/config_manager.i"
	@echo "... src/common/config_manager.s"
	@echo "... src/device/motor_controller.o"
	@echo "... src/device/motor_controller.i"
	@echo "... src/device/motor_controller.s"
	@echo "... src/device/power_controller.o"
	@echo "... src/device/power_controller.i"
	@echo "... src/device/power_controller.s"
	@echo "... src/driver/communication_driver_factory.o"
	@echo "... src/driver/communication_driver_factory.i"
	@echo "... src/driver/communication_driver_factory.s"
	@echo "... src/driver/socket_can_driver.o"
	@echo "... src/driver/socket_can_driver.i"
	@echo "... src/driver/socket_can_driver.s"
	@echo "... src/protocol/canopen_sdo_protocol.o"
	@echo "... src/protocol/canopen_sdo_protocol.i"
	@echo "... src/protocol/canopen_sdo_protocol.s"
	@echo "... src/protocol/communication_protocol_factory.o"
	@echo "... src/protocol/communication_protocol_factory.i"
	@echo "... src/protocol/communication_protocol_factory.s"
	@echo "... src/protocol/modbus_rtu_protocol.o"
	@echo "... src/protocol/modbus_rtu_protocol.i"
	@echo "... src/protocol/modbus_rtu_protocol.s"
	@echo "... src/ros_node/pmu_main.o"
	@echo "... src/ros_node/pmu_main.i"
	@echo "... src/ros_node/pmu_main.s"
	@echo "... src/ros_node/pmu_ros_node.o"
	@echo "... src/ros_node/pmu_ros_node.i"
	@echo "... src/ros_node/pmu_ros_node.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

