cmake_minimum_required(VERSION 3.8)
project(sl_pmu)

# Default to C++17
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(sl_vcu_all REQUIRED)

# Find system dependencies
find_package(PkgConfig REQUIRED)
pkg_check_modules(LIBMODBUS REQUIRED libmodbus)

# Include directories
include_directories(include)
include_directories(${LIBMODBUS_INCLUDE_DIRS})

# Define dependencies list
set(DEPENDENCIES
  rclcpp
  std_msgs
  geometry_msgs
  nav_msgs
  sensor_msgs
  tf2
  tf2_ros
  tf2_geometry_msgs
  sl_vcu_all
)

# Common library
add_library(${PROJECT_NAME}_common
  src/common/config_manager.cpp
)
ament_target_dependencies(${PROJECT_NAME}_common ${DEPENDENCIES})

# Driver library
add_library(${PROJECT_NAME}_driver
  src/driver/socket_can_driver.cpp
  src/driver/serial_driver.cpp
  src/driver/communication_driver_factory.cpp
)
ament_target_dependencies(${PROJECT_NAME}_driver ${DEPENDENCIES})
target_link_libraries(${PROJECT_NAME}_driver ${PROJECT_NAME}_common)

# Protocol library
add_library(${PROJECT_NAME}_protocol
  src/protocol/canopen_sdo_protocol.cpp
  src/protocol/modbus_rtu_protocol.cpp
  src/protocol/communication_protocol_factory.cpp
)
ament_target_dependencies(${PROJECT_NAME}_protocol ${DEPENDENCIES})
target_link_libraries(${PROJECT_NAME}_protocol 
  ${PROJECT_NAME}_common 
  ${PROJECT_NAME}_driver
  ${LIBMODBUS_LIBRARIES}
)
target_compile_options(${PROJECT_NAME}_protocol PRIVATE ${LIBMODBUS_CFLAGS_OTHER})

# Device library
add_library(${PROJECT_NAME}_device
  src/device/motor_controller.cpp
  src/device/power_controller.cpp
)
ament_target_dependencies(${PROJECT_NAME}_device ${DEPENDENCIES})
target_link_libraries(${PROJECT_NAME}_device 
  ${PROJECT_NAME}_common 
  ${PROJECT_NAME}_protocol
)

# Application library
add_library(${PROJECT_NAME}_application
  src/application/pmu_application.cpp
)
ament_target_dependencies(${PROJECT_NAME}_application ${DEPENDENCIES})
target_link_libraries(${PROJECT_NAME}_application 
  ${PROJECT_NAME}_common 
  ${PROJECT_NAME}_device
)

# ROS Node library
add_library(${PROJECT_NAME}_ros_node
  src/ros_node/pmu_ros_node.cpp
)
ament_target_dependencies(${PROJECT_NAME}_ros_node ${DEPENDENCIES})
target_link_libraries(${PROJECT_NAME}_ros_node 
  ${PROJECT_NAME}_common 
  ${PROJECT_NAME}_application
  ${PROJECT_NAME}_device
  ${PROJECT_NAME}_protocol
  ${PROJECT_NAME}_driver
)

# Main executable
add_executable(pmu_node
  src/ros_node/pmu_main.cpp
)
ament_target_dependencies(pmu_node ${DEPENDENCIES})
target_link_libraries(pmu_node ${PROJECT_NAME}_ros_node)

# Install targets
install(TARGETS
  ${PROJECT_NAME}_common
  ${PROJECT_NAME}_driver
  ${PROJECT_NAME}_protocol
  ${PROJECT_NAME}_device
  ${PROJECT_NAME}_application
  ${PROJECT_NAME}_ros_node
  DESTINATION lib/${PROJECT_NAME}
)

install(TARGETS
  pmu_node
  DESTINATION lib/${PROJECT_NAME}
)

# Install header files
install(DIRECTORY include/
  DESTINATION include/
)

# Install config files
install(DIRECTORY config/
  DESTINATION share/${PROJECT_NAME}/config/
)

# Install launch files
install(DIRECTORY launch/
  DESTINATION share/${PROJECT_NAME}/launch/
)

# Install documentation
install(DIRECTORY doc/
  DESTINATION share/${PROJECT_NAME}/doc/
)

# Testing
if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  find_package(ament_cmake_gtest REQUIRED)
  
  # Linting
  ament_lint_auto_find_test_dependencies()
  
  # Unit tests can be added here
  # ament_add_gtest(${PROJECT_NAME}_test test/test_example.cpp)
  # if(TARGET ${PROJECT_NAME}_test)
  #   target_link_libraries(${PROJECT_NAME}_test ${PROJECT_NAME}_common)
  # endif()
endif()

ament_package()
