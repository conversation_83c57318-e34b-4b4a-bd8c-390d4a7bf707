#include "sl_pmu/protocol/communication_protocol.hpp"
#include <memory>
#include <string>
#include <algorithm>

namespace sl_pmu {
namespace protocol {

/**
 * @brief Mock protocol for testing purposes
 */
class MockProtocol : public ICommunicationProtocol {
public:
    MockProtocol() : initialized_(false), node_id_(1) {}
    
    bool initialize(std::shared_ptr<driver::ICommunicationDriver> driver) override {
        driver_ = driver;
        initialized_ = (driver != nullptr);
        return initialized_;
    }
    
    void shutdown() override {
        initialized_ = false;
        driver_.reset();
    }
    
    bool isReady() const override {
        return initialized_ && driver_ && driver_->isReady();
    }
    
    common::AsyncResult readRegister(uint16_t address, uint8_t sub_address, uint32_t& data) override {
        (void)address;
        (void)sub_address;
        if (!isReady()) {
            return common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Mock protocol not ready");
        }
        data = 0x12345678;  // Mock data
        return common::AsyncResult(common::ResultCode::SUCCESS);
    }
    
    common::AsyncResult writeRegister(uint16_t address, uint8_t sub_address, uint32_t data) override {
        (void)address;
        (void)sub_address;
        (void)data;
        if (!isReady()) {
            return common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Mock protocol not ready");
        }
        return common::AsyncResult(common::ResultCode::SUCCESS);
    }
    
    void readRegisterAsync(uint16_t address, uint8_t sub_address,
                          common::AsyncCallback callback,
                          const common::TimeoutConfig& timeout_config) override {
        (void)timeout_config;
        uint32_t data;
        auto result = readRegister(address, sub_address, data);
        result.data = data;
        if (callback) {
            callback(result);
        }
    }
    
    void writeRegisterAsync(uint16_t address, uint8_t sub_address, uint32_t data,
                           common::AsyncCallback callback,
                           const common::TimeoutConfig& timeout_config) override {
        (void)timeout_config;
        auto result = writeRegister(address, sub_address, data);
        if (callback) {
            callback(result);
        }
    }
    
    std::string getProtocolType() const override {
        return "Mock";
    }
    
    void setNodeId(uint8_t node_id) override {
        node_id_ = node_id;
    }
    
    uint8_t getNodeId() const override {
        return node_id_;
    }

private:
    bool initialized_;
    uint8_t node_id_;
    std::shared_ptr<driver::ICommunicationDriver> driver_;
};

// Factory implementation
std::unique_ptr<ICommunicationProtocol> CommunicationProtocolFactory::createProtocol(ProtocolType type) {
    switch (type) {
        case ProtocolType::CANOPEN_SDO:
            return std::make_unique<CanopenSdoProtocol>();
        case ProtocolType::MODBUS_RTU:
            return std::make_unique<ModbusRtuProtocol>();
        case ProtocolType::MOCK:
            return std::make_unique<MockProtocol>();
        default:
            return nullptr;
    }
}

std::unique_ptr<ICommunicationProtocol> CommunicationProtocolFactory::createProtocol(const std::string& type_name) {
    std::string lower_type = type_name;
    std::transform(lower_type.begin(), lower_type.end(), lower_type.begin(), ::tolower);
    
    if (lower_type == "canopen_sdo" || lower_type == "canopen" || lower_type == "sdo") {
        return createProtocol(ProtocolType::CANOPEN_SDO);
    } else if (lower_type == "modbus_rtu" || lower_type == "modbus" || lower_type == "rtu") {
        return createProtocol(ProtocolType::MODBUS_RTU);
    } else if (lower_type == "mock" || lower_type == "test") {
        return createProtocol(ProtocolType::MOCK);
    } else {
        return nullptr;
    }
}

} // namespace protocol
} // namespace sl_pmu
