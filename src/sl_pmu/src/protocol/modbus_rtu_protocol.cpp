#include "sl_pmu/protocol/communication_protocol.hpp"
#include <modbus.h>
#include <mutex>
#include <chrono>
#include <thread>
#include <atomic>
#include <condition_variable>

namespace sl_pmu {
namespace protocol {

class ModbusRtuProtocol::Impl {
public:
    Impl() : modbus_ctx_(nullptr), initialized_(false), node_id_(1) {}

    ~Impl() {
        shutdown();
    }

    bool initialize(std::shared_ptr<driver::ICommunicationDriver> driver) {
        if (initialized_) {
            return true;
        }

        // For Modbus RTU, we don't use the driver interface directly
        // Instead, we use libmodbus which handles the serial communication
        driver_ = driver;
        initialized_ = true;
        return true;
    }

    bool initializeModbus(const std::string& device, int baud_rate, char parity, 
                         int data_bits, int stop_bits, int slave_address) {
        std::lock_guard<std::mutex> lock(modbus_mutex_);

        if (modbus_ctx_ != nullptr) {
            modbus_close(modbus_ctx_);
            modbus_free(modbus_ctx_);
            modbus_ctx_ = nullptr;
        }

        // Create new RTU context
        modbus_ctx_ = modbus_new_rtu(device.c_str(), baud_rate, parity, data_bits, stop_bits);
        if (modbus_ctx_ == nullptr) {
            return false;
        }

        // Set slave address
        if (modbus_set_slave(modbus_ctx_, slave_address) == -1) {
            modbus_free(modbus_ctx_);
            modbus_ctx_ = nullptr;
            return false;
        }

        // Set response timeout
        modbus_set_response_timeout(modbus_ctx_, 1, 0);  // 1 second timeout

        // Connect to the device
        if (modbus_connect(modbus_ctx_) == -1) {
            modbus_free(modbus_ctx_);
            modbus_ctx_ = nullptr;
            return false;
        }

        node_id_ = slave_address;
        return true;
    }

    void shutdown() {
        if (!initialized_) {
            return;
        }

        std::lock_guard<std::mutex> lock(modbus_mutex_);
        if (modbus_ctx_ != nullptr) {
            modbus_close(modbus_ctx_);
            modbus_free(modbus_ctx_);
            modbus_ctx_ = nullptr;
        }

        driver_.reset();
        initialized_ = false;
    }

    bool isReady() const {
        std::lock_guard<std::mutex> lock(modbus_mutex_);
        return initialized_ && modbus_ctx_ != nullptr;
    }

    void setNodeId(uint8_t node_id) {
        std::lock_guard<std::mutex> lock(modbus_mutex_);
        node_id_ = node_id;
        if (modbus_ctx_ != nullptr) {
            modbus_set_slave(modbus_ctx_, node_id);
        }
    }

    uint8_t getNodeId() const {
        return node_id_;
    }

    common::AsyncResult readRegister(uint16_t address, uint8_t sub_address, uint32_t& data) {
        (void)sub_address;  // Modbus doesn't use sub-addresses
        
        std::lock_guard<std::mutex> lock(modbus_mutex_);
        
        if (!isReady()) {
            return common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Modbus not ready");
        }

        uint16_t dest[2];  // Read up to 2 registers for 32-bit data
        int rc = modbus_read_registers(modbus_ctx_, address, 1, dest);
        if (rc == -1) {
            return common::AsyncResult(common::ResultCode::COMMUNICATION_ERROR, 
                                     "Failed to read register: " + std::string(modbus_strerror(errno)));
        }

        data = dest[0];
        return common::AsyncResult(common::ResultCode::SUCCESS, "", data);
    }

    common::AsyncResult readRegisters(uint16_t address, uint16_t count, uint16_t* dest) {
        std::lock_guard<std::mutex> lock(modbus_mutex_);
        
        if (!isReady()) {
            return common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Modbus not ready");
        }

        int rc = modbus_read_registers(modbus_ctx_, address, count, dest);
        if (rc == -1) {
            return common::AsyncResult(common::ResultCode::COMMUNICATION_ERROR, 
                                     "Failed to read registers: " + std::string(modbus_strerror(errno)));
        }

        return common::AsyncResult(common::ResultCode::SUCCESS);
    }

    common::AsyncResult writeRegister(uint16_t address, uint8_t sub_address, uint32_t data) {
        (void)sub_address;  // Modbus doesn't use sub-addresses
        
        std::lock_guard<std::mutex> lock(modbus_mutex_);
        
        if (!isReady()) {
            return common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Modbus not ready");
        }

        uint16_t value = static_cast<uint16_t>(data & 0xFFFF);
        int rc = modbus_write_register(modbus_ctx_, address, value);
        if (rc == -1) {
            return common::AsyncResult(common::ResultCode::COMMUNICATION_ERROR, 
                                     "Failed to write register: " + std::string(modbus_strerror(errno)));
        }

        return common::AsyncResult(common::ResultCode::SUCCESS);
    }

    common::AsyncResult writeRegisters(uint16_t address, uint16_t count, uint16_t* values) {
        std::lock_guard<std::mutex> lock(modbus_mutex_);
        
        if (!isReady()) {
            return common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Modbus not ready");
        }

        int rc = modbus_write_registers(modbus_ctx_, address, count, values);
        if (rc == -1) {
            return common::AsyncResult(common::ResultCode::COMMUNICATION_ERROR, 
                                     "Failed to write registers: " + std::string(modbus_strerror(errno)));
        }

        return common::AsyncResult(common::ResultCode::SUCCESS);
    }

    void readRegisterAsync(uint16_t address, uint8_t sub_address,
                          common::AsyncCallback callback,
                          const common::TimeoutConfig& timeout_config) {
        // For libmodbus, we execute synchronously in a separate thread
        std::thread([this, address, sub_address, callback, timeout_config]() {
            uint32_t data;
            auto result = readRegister(address, sub_address, data);
            result.data = data;
            
            if (callback) {
                callback(result);
            }
        }).detach();
    }

    void writeRegisterAsync(uint16_t address, uint8_t sub_address, uint32_t data,
                           common::AsyncCallback callback,
                           const common::TimeoutConfig& timeout_config) {
        // For libmodbus, we execute synchronously in a separate thread
        std::thread([this, address, sub_address, data, callback, timeout_config]() {
            auto result = writeRegister(address, sub_address, data);
            
            if (callback) {
                callback(result);
            }
        }).detach();
    }

    std::string getProtocolType() const {
        return "Modbus RTU (libmodbus)";
    }

private:
    std::shared_ptr<driver::ICommunicationDriver> driver_;
    modbus_t* modbus_ctx_;
    std::atomic<bool> initialized_;
    uint8_t node_id_;
    mutable std::mutex modbus_mutex_;
};

// ModbusRtuProtocol implementation
ModbusRtuProtocol::ModbusRtuProtocol() : pImpl_(std::make_unique<Impl>()) {}

ModbusRtuProtocol::~ModbusRtuProtocol() = default;

bool ModbusRtuProtocol::initialize(std::shared_ptr<driver::ICommunicationDriver> driver) {
    return pImpl_->initialize(driver);
}

bool ModbusRtuProtocol::initializeModbus(const std::string& device, int baud_rate, char parity, 
                                        int data_bits, int stop_bits, int slave_address) {
    return pImpl_->initializeModbus(device, baud_rate, parity, data_bits, stop_bits, slave_address);
}

void ModbusRtuProtocol::shutdown() {
    pImpl_->shutdown();
}

bool ModbusRtuProtocol::isReady() const {
    return pImpl_->isReady();
}

common::AsyncResult ModbusRtuProtocol::readRegister(uint16_t address, uint8_t sub_address, uint32_t& data) {
    return pImpl_->readRegister(address, sub_address, data);
}

common::AsyncResult ModbusRtuProtocol::writeRegister(uint16_t address, uint8_t sub_address, uint32_t data) {
    return pImpl_->writeRegister(address, sub_address, data);
}

void ModbusRtuProtocol::readRegisterAsync(uint16_t address, uint8_t sub_address,
                                         common::AsyncCallback callback,
                                         const common::TimeoutConfig& timeout_config) {
    pImpl_->readRegisterAsync(address, sub_address, callback, timeout_config);
}

void ModbusRtuProtocol::writeRegisterAsync(uint16_t address, uint8_t sub_address, uint32_t data,
                                          common::AsyncCallback callback,
                                          const common::TimeoutConfig& timeout_config) {
    pImpl_->writeRegisterAsync(address, sub_address, data, callback, timeout_config);
}

std::string ModbusRtuProtocol::getProtocolType() const {
    return pImpl_->getProtocolType();
}

void ModbusRtuProtocol::setNodeId(uint8_t node_id) {
    pImpl_->setNodeId(node_id);
}

uint8_t ModbusRtuProtocol::getNodeId() const {
    return pImpl_->getNodeId();
}

} // namespace protocol
} // namespace sl_pmu
