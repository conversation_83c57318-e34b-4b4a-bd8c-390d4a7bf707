#include "sl_pmu/protocol/communication_protocol.hpp"
#include <map>
#include <mutex>
#include <chrono>
#include <thread>
#include <atomic>
#include <condition_variable>

namespace sl_pmu {
namespace protocol {

// CANopen SDO command codes
namespace sdo_commands {
    constexpr uint8_t READ_REQUEST = 0x40;
    constexpr uint8_t READ_RESPONSE_1BYTE = 0x4F;
    constexpr uint8_t READ_RESPONSE_2BYTE = 0x4B;
    constexpr uint8_t READ_RESPONSE_4BYTE = 0x43;
    constexpr uint8_t READ_RESPONSE_ERROR = 0x80;

    constexpr uint8_t WRITE_REQUEST_1BYTE = 0x2F;
    constexpr uint8_t WRITE_REQUEST_2BYTE = 0x2B;
    constexpr uint8_t WRITE_REQUEST_4BYTE = 0x23;
    constexpr uint8_t WRITE_RESPONSE_OK = 0x60;
    constexpr uint8_t WRITE_RESPONSE_ERROR = 0x80;

    bool isReadResponseOk(uint8_t cmd) {
        return (cmd == READ_RESPONSE_1BYTE || cmd == READ_RESPONSE_2BYTE || cmd == READ_RESPONSE_4BYTE);
    }

    uint8_t getWriteCommand(uint32_t data) {
        if (data <= 0xFF) {
            return WRITE_REQUEST_1BYTE;
        } else if (data <= 0xFFFF) {
            return WRITE_REQUEST_2BYTE;
        } else {
            return WRITE_REQUEST_4BYTE;
        }
    }
}

class CanopenSdoProtocol::Impl {
public:
    struct PendingRequest {
        uint16_t index;
        uint8_t sub_index;
        std::shared_ptr<common::ResponseWaiter> waiter;
        std::chrono::steady_clock::time_point timestamp;
        std::chrono::milliseconds timeout;
        bool is_read;
    };

    Impl() : node_id_(1), initialized_(false), can_id_tx_(0x600), can_id_rx_(0x580), running_(false) {}

    ~Impl() {
        shutdown();
    }

    bool initialize(std::shared_ptr<driver::ICommunicationDriver> driver) {
        if (initialized_) {
            return true;
        }

        driver_ = driver;
        if (!driver_ || !driver_->isReady()) {
            return false;
        }

        // Set up receive callback
        driver_->setReceiveCallback([this](const common::CommFrame& frame) {
            handleReceivedFrame(frame);
        });

        // Start timeout checking thread
        running_ = true;
        timeout_thread_ = std::thread(&Impl::timeoutThreadFunction, this);

        initialized_ = true;
        return true;
    }

    void shutdown() {
        if (!initialized_) {
            return;
        }

        running_ = false;
        if (timeout_thread_.joinable()) {
            timeout_thread_.join();
        }

        // Clear pending requests
        {
            std::lock_guard<std::mutex> lock(pending_requests_mutex_);
            for (auto& pair : pending_requests_) {
                if (pair.second.waiter) {
                    pair.second.waiter->signalResponse(
                        common::AsyncResult(common::ResultCode::UNKNOWN_ERROR, "Protocol shutdown"));
                }
            }
            pending_requests_.clear();
        }

        driver_.reset();
        initialized_ = false;
    }

    bool isReady() const {
        return initialized_ && driver_ && driver_->isReady();
    }

    void setNodeId(uint8_t node_id) {
        node_id_ = node_id;
        can_id_tx_ = 0x600 + node_id;
        can_id_rx_ = 0x580 + node_id;
    }

    uint8_t getNodeId() const {
        return node_id_;
    }

    common::AsyncResult readRegister(uint16_t address, uint8_t sub_address, uint32_t& data) {
        if (!isReady()) {
            return common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Protocol not ready");
        }

        auto waiter = std::make_shared<common::ResponseWaiter>();
        
        // Create request key
        uint32_t request_key = (static_cast<uint32_t>(address) << 8) | sub_address;

        // Add to pending requests
        {
            std::lock_guard<std::mutex> lock(pending_requests_mutex_);
            PendingRequest request;
            request.index = address;
            request.sub_index = sub_address;
            request.waiter = waiter;
            request.timestamp = std::chrono::steady_clock::now();
            request.timeout = std::chrono::milliseconds(1000);
            request.is_read = true;
            pending_requests_[request_key] = request;
        }

        // Create and send SDO read request frame
        common::CommFrame frame;
        frame.id = can_id_tx_;
        frame.length = 8;
        frame.is_extended = false;
        frame.is_rtr = false;

        frame.data[0] = sdo_commands::READ_REQUEST;
        frame.data[1] = static_cast<uint8_t>(address & 0xFF);
        frame.data[2] = static_cast<uint8_t>((address >> 8) & 0xFF);
        frame.data[3] = sub_address;
        frame.data[4] = 0;
        frame.data[5] = 0;
        frame.data[6] = 0;
        frame.data[7] = 0;

        auto send_result = driver_->sendFrame(frame);
        if (!send_result.isSuccess()) {
            // Remove from pending requests
            std::lock_guard<std::mutex> lock(pending_requests_mutex_);
            pending_requests_.erase(request_key);
            return send_result;
        }

        // Wait for response
        if (waiter->waitForResponse(std::chrono::milliseconds(1000))) {
            data = waiter->result.data;
            return waiter->result;
        } else {
            // Timeout - remove from pending requests
            std::lock_guard<std::mutex> lock(pending_requests_mutex_);
            pending_requests_.erase(request_key);
            return common::AsyncResult(common::ResultCode::TIMEOUT, "SDO read timeout");
        }
    }

    common::AsyncResult writeRegister(uint16_t address, uint8_t sub_address, uint32_t data) {
        if (!isReady()) {
            return common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Protocol not ready");
        }

        auto waiter = std::make_shared<common::ResponseWaiter>();
        
        // Create request key
        uint32_t request_key = (static_cast<uint32_t>(address) << 8) | sub_address;

        // Add to pending requests
        {
            std::lock_guard<std::mutex> lock(pending_requests_mutex_);
            PendingRequest request;
            request.index = address;
            request.sub_index = sub_address;
            request.waiter = waiter;
            request.timestamp = std::chrono::steady_clock::now();
            request.timeout = std::chrono::milliseconds(1000);
            request.is_read = false;
            pending_requests_[request_key] = request;
        }

        // Determine data size and command explicitly
        uint8_t command = sdo_commands::getWriteCommand(data);

        // Create and send SDO write request frame
        common::CommFrame frame;
        frame.id = can_id_tx_;
        frame.length = 8;
        frame.is_extended = false;
        frame.is_rtr = false;

        frame.data[0] = command;
        frame.data[1] = static_cast<uint8_t>(address & 0xFF);
        frame.data[2] = static_cast<uint8_t>((address >> 8) & 0xFF);
        frame.data[3] = sub_address;
        frame.data[4] = static_cast<uint8_t>(data & 0xFF);
        frame.data[5] = static_cast<uint8_t>((data >> 8) & 0xFF);
        frame.data[6] = static_cast<uint8_t>((data >> 16) & 0xFF);
        frame.data[7] = static_cast<uint8_t>((data >> 24) & 0xFF);

        auto send_result = driver_->sendFrame(frame);
        if (!send_result.isSuccess()) {
            // Remove from pending requests
            std::lock_guard<std::mutex> lock(pending_requests_mutex_);
            pending_requests_.erase(request_key);
            return send_result;
        }

        // Wait for response
        if (waiter->waitForResponse(std::chrono::milliseconds(1000))) {
            return waiter->result;
        } else {
            // Timeout - remove from pending requests
            std::lock_guard<std::mutex> lock(pending_requests_mutex_);
            pending_requests_.erase(request_key);
            return common::AsyncResult(common::ResultCode::TIMEOUT, "SDO write timeout");
        }
    }

    void readRegisterAsync(uint16_t address, uint8_t sub_address,
                          common::AsyncCallback callback,
                          const common::TimeoutConfig& timeout_config) {
        std::thread([this, address, sub_address, callback, timeout_config]() {
            uint32_t data;
            auto result = readRegister(address, sub_address, data);
            result.data = data;
            
            if (callback) {
                callback(result);
            }
        }).detach();
    }

    void writeRegisterAsync(uint16_t address, uint8_t sub_address, uint32_t data,
                           common::AsyncCallback callback,
                           const common::TimeoutConfig& timeout_config) {
        std::thread([this, address, sub_address, data, callback, timeout_config]() {
            auto result = writeRegister(address, sub_address, data);
            
            if (callback) {
                callback(result);
            }
        }).detach();
    }

    std::string getProtocolType() const {
        return "CANopen SDO";
    }

private:
    void handleReceivedFrame(const common::CommFrame& frame) {
        // Check if this is a response from our node
        if (frame.id != can_id_rx_ || frame.length < 4) {
            return;
        }

        uint8_t command = frame.data[0];
        uint16_t index = static_cast<uint16_t>(frame.data[1]) | (static_cast<uint16_t>(frame.data[2]) << 8);
        uint8_t sub_index = frame.data[3];

        uint32_t request_key = (static_cast<uint32_t>(index) << 8) | sub_index;

        std::lock_guard<std::mutex> lock(pending_requests_mutex_);
        auto it = pending_requests_.find(request_key);
        if (it == pending_requests_.end()) {
            return;  // No pending request for this response
        }

        PendingRequest request = it->second;
        pending_requests_.erase(it);

        // Process response
        common::AsyncResult result;
        if (command == sdo_commands::READ_RESPONSE_ERROR || command == sdo_commands::WRITE_RESPONSE_ERROR) {
            uint32_t error_code = 0;
            if (frame.length >= 8) {
                error_code = static_cast<uint32_t>(frame.data[4]) |
                           (static_cast<uint32_t>(frame.data[5]) << 8) |
                           (static_cast<uint32_t>(frame.data[6]) << 16) |
                           (static_cast<uint32_t>(frame.data[7]) << 24);
            }
            result = common::AsyncResult(common::ResultCode::PROTOCOL_ERROR, 
                                       "SDO error response: 0x" + std::to_string(error_code), error_code);
        } else if (request.is_read && sdo_commands::isReadResponseOk(command)) {
            // Extract data from read response
            uint32_t data = 0;
            if (frame.length >= 8) {
                data = static_cast<uint32_t>(frame.data[4]) |
                      (static_cast<uint32_t>(frame.data[5]) << 8) |
                      (static_cast<uint32_t>(frame.data[6]) << 16) |
                      (static_cast<uint32_t>(frame.data[7]) << 24);
            }
            result = common::AsyncResult(common::ResultCode::SUCCESS, "", data);
        } else if (!request.is_read && command == sdo_commands::WRITE_RESPONSE_OK) {
            result = common::AsyncResult(common::ResultCode::SUCCESS);
        } else {
            result = common::AsyncResult(common::ResultCode::PROTOCOL_ERROR, "Unexpected SDO response");
        }

        // Signal response received
        if (request.waiter) {
            request.waiter->signalResponse(result);
        }
    }

    void timeoutThreadFunction() {
        while (running_) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));

            std::vector<std::shared_ptr<common::ResponseWaiter>> timed_out_waiters;
            auto now = std::chrono::steady_clock::now();

            {
                std::lock_guard<std::mutex> lock(pending_requests_mutex_);
                auto it = pending_requests_.begin();
                while (it != pending_requests_.end()) {
                    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - it->second.timestamp);
                    if (elapsed > it->second.timeout) {
                        timed_out_waiters.push_back(it->second.waiter);
                        it = pending_requests_.erase(it);
                    } else {
                        ++it;
                    }
                }
            }

            // Signal timeout for timed out requests
            for (const auto& waiter : timed_out_waiters) {
                if (waiter) {
                    waiter->signalResponse(common::AsyncResult(common::ResultCode::TIMEOUT, "SDO request timeout"));
                }
            }
        }
    }

    std::shared_ptr<driver::ICommunicationDriver> driver_;
    uint8_t node_id_;
    uint32_t can_id_tx_;
    uint32_t can_id_rx_;
    std::atomic<bool> initialized_;
    std::atomic<bool> running_;

    std::map<uint32_t, PendingRequest> pending_requests_;
    std::mutex pending_requests_mutex_;

    std::thread timeout_thread_;
};

// CanopenSdoProtocol implementation
CanopenSdoProtocol::CanopenSdoProtocol() : pImpl_(std::make_unique<Impl>()) {}

CanopenSdoProtocol::~CanopenSdoProtocol() = default;

bool CanopenSdoProtocol::initialize(std::shared_ptr<driver::ICommunicationDriver> driver) {
    return pImpl_->initialize(driver);
}

void CanopenSdoProtocol::shutdown() {
    pImpl_->shutdown();
}

bool CanopenSdoProtocol::isReady() const {
    return pImpl_->isReady();
}

common::AsyncResult CanopenSdoProtocol::readRegister(uint16_t address, uint8_t sub_address, uint32_t& data) {
    return pImpl_->readRegister(address, sub_address, data);
}

common::AsyncResult CanopenSdoProtocol::writeRegister(uint16_t address, uint8_t sub_address, uint32_t data) {
    return pImpl_->writeRegister(address, sub_address, data);
}

void CanopenSdoProtocol::readRegisterAsync(uint16_t address, uint8_t sub_address,
                                          common::AsyncCallback callback,
                                          const common::TimeoutConfig& timeout_config) {
    pImpl_->readRegisterAsync(address, sub_address, callback, timeout_config);
}

void CanopenSdoProtocol::writeRegisterAsync(uint16_t address, uint8_t sub_address, uint32_t data,
                                           common::AsyncCallback callback,
                                           const common::TimeoutConfig& timeout_config) {
    pImpl_->writeRegisterAsync(address, sub_address, data, callback, timeout_config);
}

std::string CanopenSdoProtocol::getProtocolType() const {
    return pImpl_->getProtocolType();
}

void CanopenSdoProtocol::setNodeId(uint8_t node_id) {
    pImpl_->setNodeId(node_id);
}

uint8_t CanopenSdoProtocol::getNodeId() const {
    return pImpl_->getNodeId();
}

} // namespace protocol
} // namespace sl_pmu
