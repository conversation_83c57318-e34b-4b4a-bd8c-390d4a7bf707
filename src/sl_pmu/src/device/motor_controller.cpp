#include "sl_pmu/device/motor_controller.hpp"
#include <atomic>
#include <mutex>
#include <cmath>

namespace sl_pmu {
namespace device {

class MotorController::Impl {
public:
    Impl() : initialized_(false), motors_enabled_(false), emergency_stop_trigger_level_(0) {}

    ~Impl() {
        shutdown();
    }

    bool initialize(std::shared_ptr<protocol::ICommunicationProtocol> protocol,
                   const common::RobotParameters& params) {
        if (initialized_) {
            return true;
        }

        protocol_ = protocol;
        robot_params_ = params;

        if (!protocol_ || !protocol_->isReady()) {
            return false;
        }

        // Determine protocol type and set appropriate constants
        protocol_type_ = protocol_->getProtocolType();
        is_modbus_ = (protocol_type_.find("Modbus") != std::string::npos);

        initialized_ = true;
        return true;
    }

    void shutdown() {
        if (!initialized_) {
            return;
        }

        // Disable motors before shutdown
        if (motors_enabled_) {
            disableMotors([](const common::AsyncResult&) {}, {});
        }

        protocol_.reset();
        initialized_ = false;
    }

    bool isReady() const {
        return initialized_ && protocol_ && protocol_->isReady();
    }

    void enableMotors(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"));
            }
            return;
        }

        if (is_modbus_) {
            enableMotorsModbus(callback, timeout_config);
        } else {
            enableMotorsCanopen(callback, timeout_config);
        }
    }

    void disableMotors(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"));
            }
            return;
        }

        if (is_modbus_) {
            protocol_->writeRegisterAsync(
                motor_constants::modbus::REG_CONTROL, 
                0, 
                motor_constants::modbus::CTRL_DISABLE,
                [this, callback](const common::AsyncResult& result) {
                    if (result.isSuccess()) {
                        motors_enabled_ = false;
                    }
                    if (callback) {
                        callback(result);
                    }
                },
                timeout_config
            );
        } else {
            protocol_->writeRegisterAsync(
                motor_constants::canopen::INDEX_CTRL, 
                motor_constants::canopen::SUBINDEX_DEFAULT, 
                motor_constants::canopen::CTRL_RELEASE,
                [this, callback](const common::AsyncResult& result) {
                    if (result.isSuccess()) {
                        motors_enabled_ = false;
                    }
                    if (callback) {
                        callback(result);
                    }
                },
                timeout_config
            );
        }
    }

    void setMotorSpeeds(const common::MotorSpeeds& speeds,
                       common::AsyncCallback callback,
                       const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"));
            }
            return;
        }

        if (!motors_enabled_) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Motors not enabled"));
            }
            return;
        }

        if (is_modbus_) {
            setMotorSpeedsModbus(speeds, callback, timeout_config);
        } else {
            setMotorSpeedsCanopen(speeds, callback, timeout_config);
        }
    }

    void readMotorPositions(std::function<void(const common::AsyncResult&, const common::MotorPositions&)> callback,
                           const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"), {});
            }
            return;
        }

        if (is_modbus_) {
            readMotorPositionsModbus(callback, timeout_config);
        } else {
            readMotorPositionsCanopen(callback, timeout_config);
        }
    }

    void readMotorSpeeds(std::function<void(const common::AsyncResult&, const common::MotorSpeeds&)> callback,
                        const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"), {});
            }
            return;
        }

        if (is_modbus_) {
            readMotorSpeedsModbus(callback, timeout_config);
        } else {
            readMotorSpeedsCanopen(callback, timeout_config);
        }
    }

    void readMotorCurrents(std::function<void(const common::AsyncResult&, const common::MotorCurrents&)> callback,
                          const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"), {});
            }
            return;
        }

        if (is_modbus_) {
            readMotorCurrentsModbus(callback, timeout_config);
        } else {
            readMotorCurrentsCanopen(callback, timeout_config);
        }
    }

    void readMotorTemperatures(std::function<void(const common::AsyncResult&, const common::MotorTemperatures&)> callback,
                              const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"), {});
            }
            return;
        }

        if (is_modbus_) {
            readMotorTemperaturesModbus(callback, timeout_config);
        } else {
            readMotorTemperaturesCanopen(callback, timeout_config);
        }
    }

    void readGpioStatus(std::function<void(const common::AsyncResult&, const common::GpioStatus&)> callback,
                       const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"), {});
            }
            return;
        }

        uint16_t gpio_reg = is_modbus_ ? motor_constants::modbus::REG_GPIO_STATUS : 
                                        motor_constants::canopen::INDEX_DIN_STATUS;
        uint8_t sub_addr = is_modbus_ ? 0 : motor_constants::canopen::SUBINDEX_DEFAULT;

        protocol_->readRegisterAsync(
            gpio_reg,
            sub_addr,
            [callback](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result, {});
                    }
                    return;
                }

                common::GpioStatus gpio_status(result.data);
                
                if (callback) {
                    callback(common::AsyncResult(common::ResultCode::SUCCESS), gpio_status);
                }
            },
            timeout_config
        );
    }

    void readLastAlarm(std::function<void(const common::AsyncResult&, uint32_t)> callback,
                      const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"), 0);
            }
            return;
        }

        if (is_modbus_) {
            // For Modbus, read both left and right alarm registers
            protocol_->readRegisterAsync(
                motor_constants::modbus::REG_LEFT_ALARM,
                0,
                [this, callback, timeout_config](const common::AsyncResult& result) {
                    if (!result.isSuccess()) {
                        if (callback) {
                            callback(result, 0);
                        }
                        return;
                    }

                    uint32_t left_alarm = result.data;
                    
                    // Read right alarm
                    protocol_->readRegisterAsync(
                        motor_constants::modbus::REG_RIGHT_ALARM,
                        0,
                        [callback, left_alarm](const common::AsyncResult& result2) {
                            if (!result2.isSuccess()) {
                                if (callback) {
                                    callback(result2, 0);
                                }
                                return;
                            }

                            uint32_t right_alarm = result2.data;
                            uint32_t combined_alarm = (left_alarm != 0) ? left_alarm : right_alarm;
                            
                            if (callback) {
                                callback(common::AsyncResult(common::ResultCode::SUCCESS), combined_alarm);
                            }
                        },
                        timeout_config
                    );
                },
                timeout_config
            );
        } else {
            protocol_->readRegisterAsync(
                motor_constants::canopen::INDEX_LAST_ALARM,
                motor_constants::canopen::SUBINDEX_LAST_ALARM,
                [callback](const common::AsyncResult& result) {
                    if (callback) {
                        callback(result, result.data);
                    }
                },
                timeout_config
            );
        }
    }

    void clearAlarm(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"));
            }
            return;
        }

        if (is_modbus_) {
            protocol_->writeRegisterAsync(
                motor_constants::modbus::REG_CONTROL,
                0,
                motor_constants::modbus::CTRL_CLEAR_ALARM,
                callback,
                timeout_config
            );
        } else {
            protocol_->writeRegisterAsync(
                motor_constants::canopen::INDEX_CTRL,
                motor_constants::canopen::SUBINDEX_DEFAULT,
                motor_constants::canopen::CTRL_ALARM_CLEAR,
                callback,
                timeout_config
            );
        }
    }

    std::string getDeviceType() const {
        return "Motor Controller (" + protocol_type_ + ")";
    }

    bool areMotorsEnabled() const {
        return motors_enabled_;
    }

    void setEmergencyStopTriggerLevel(int level) {
        emergency_stop_trigger_level_ = level;
    }

private:
    void enableMotorsModbus(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        // Step 1: Set speed mode
        protocol_->writeRegisterAsync(
            motor_constants::modbus::REG_MODE,
            0,
            motor_constants::modbus::MODE_SPEED,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result);
                    }
                    return;
                }
                
                // Step 2: Enable motors
                protocol_->writeRegisterAsync(
                    motor_constants::modbus::REG_CONTROL,
                    0,
                    motor_constants::modbus::CTRL_ENABLE,
                    [this, callback](const common::AsyncResult& result2) {
                        if (result2.isSuccess()) {
                            motors_enabled_ = true;
                        }
                        if (callback) {
                            callback(result2);
                        }
                    },
                    timeout_config
                );
            },
            timeout_config
        );
    }

    void enableMotorsCanopen(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        // Multi-step motor enable process for CANopen
        enableMotorsCanopenStep1(callback, timeout_config);
    }

    void enableMotorsCanopenStep1(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        // Step 1: Set speed mode
        protocol_->writeRegisterAsync(
            motor_constants::canopen::INDEX_RUN_MODE,
            motor_constants::canopen::SUBINDEX_DEFAULT,
            motor_constants::canopen::RUN_MODE_SPEED,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result);
                    }
                    return;
                }
                enableMotorsCanopenStep2(callback, timeout_config);
            },
            timeout_config
        );
    }

    void enableMotorsCanopenStep2(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        // Step 2: Release motors
        protocol_->writeRegisterAsync(
            motor_constants::canopen::INDEX_CTRL,
            motor_constants::canopen::SUBINDEX_DEFAULT,
            motor_constants::canopen::CTRL_RELEASE,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result);
                    }
                    return;
                }
                enableMotorsCanopenStep3(callback, timeout_config);
            },
            timeout_config
        );
    }

    void enableMotorsCanopenStep3(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        // Step 3: Enable motors
        protocol_->writeRegisterAsync(
            motor_constants::canopen::INDEX_CTRL,
            motor_constants::canopen::SUBINDEX_DEFAULT,
            motor_constants::canopen::CTRL_ENABLE,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result);
                    }
                    return;
                }
                enableMotorsCanopenStep4(callback, timeout_config);
            },
            timeout_config
        );
    }

    void enableMotorsCanopenStep4(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        // Step 4: Enable speed control
        protocol_->writeRegisterAsync(
            motor_constants::canopen::INDEX_CTRL,
            motor_constants::canopen::SUBINDEX_DEFAULT,
            motor_constants::canopen::CTRL_SPEED_ENABLE,
            [this, callback](const common::AsyncResult& result) {
                if (result.isSuccess()) {
                    motors_enabled_ = true;
                }
                if (callback) {
                    callback(result);
                }
            },
            timeout_config
        );
    }

    void setMotorSpeedsModbus(const common::MotorSpeeds& speeds, common::AsyncCallback callback, 
                             const common::TimeoutConfig& timeout_config) {
        // Convert RPM to register values and combine for Modbus
        int16_t left_speed = static_cast<int16_t>(speeds.left_rpm);
        int16_t right_speed = static_cast<int16_t>(-speeds.right_rpm);  // Right motor inverted
        
        uint32_t combined_speed = (static_cast<uint32_t>(right_speed & 0xFFFF) << 16) | 
                                 static_cast<uint32_t>(left_speed & 0xFFFF);

        protocol_->writeRegisterAsync(
            motor_constants::modbus::REG_SPEED_SET,
            0,
            combined_speed,
            callback,
            timeout_config
        );
    }

    void setMotorSpeedsCanopen(const common::MotorSpeeds& speeds, common::AsyncCallback callback, 
                              const common::TimeoutConfig& timeout_config) {
        // Convert RPM to encoder speed
        int32_t left_encoder_speed = rpmToEncoderSpeed(speeds.left_rpm);
        int32_t right_encoder_speed = rpmToEncoderSpeed(-speeds.right_rpm);  // Right motor is inverted

        // Combine speeds into single 32-bit value
        uint32_t combined_speed = (static_cast<uint32_t>(right_encoder_speed) << 16) | 
                                 static_cast<uint32_t>(left_encoder_speed & 0x0000FFFF);

        protocol_->writeRegisterAsync(
            motor_constants::canopen::INDEX_DEST_SPEED,
            motor_constants::canopen::SUBINDEX_L_R,
            combined_speed,
            callback,
            timeout_config
        );
    }

    void readMotorPositionsModbus(std::function<void(const common::AsyncResult&, const common::MotorPositions&)> callback,
                                 const common::TimeoutConfig& timeout_config) {
        // Read left position (2 registers)
        protocol_->readRegisterAsync(
            motor_constants::modbus::REG_LEFT_POS,
            0,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result, {});
                    }
                    return;
                }

                uint16_t left_pos_low = result.data;
                
                // Read left position high
                protocol_->readRegisterAsync(
                    motor_constants::modbus::REG_LEFT_POS + 1,
                    0,
                    [this, callback, timeout_config, left_pos_low](const common::AsyncResult& result2) {
                        if (!result2.isSuccess()) {
                            if (callback) {
                                callback(result2, {});
                            }
                            return;
                        }

                        uint16_t left_pos_high = result2.data;
                        int32_t left_pos = (static_cast<int32_t>(left_pos_high) << 16) | left_pos_low;

                        // Read right position
                        protocol_->readRegisterAsync(
                            motor_constants::modbus::REG_RIGHT_POS,
                            0,
                            [this, callback, timeout_config, left_pos](const common::AsyncResult& result3) {
                                if (!result3.isSuccess()) {
                                    if (callback) {
                                        callback(result3, {});
                                    }
                                    return;
                                }

                                uint16_t right_pos_low = result3.data;
                                
                                // Read right position high
                                protocol_->readRegisterAsync(
                                    motor_constants::modbus::REG_RIGHT_POS + 1,
                                    0,
                                    [callback, left_pos, right_pos_low](const common::AsyncResult& result4) {
                                        if (!result4.isSuccess()) {
                                            if (callback) {
                                                callback(result4, {});
                                            }
                                            return;
                                        }

                                        uint16_t right_pos_high = result4.data;
                                        int32_t right_pos = (static_cast<int32_t>(right_pos_high) << 16) | right_pos_low;
                                        
                                        common::MotorPositions positions(left_pos, right_pos);
                                        
                                        if (callback) {
                                            callback(common::AsyncResult(common::ResultCode::SUCCESS), positions);
                                        }
                                    },
                                    timeout_config
                                );
                            },
                            timeout_config
                        );
                    },
                    timeout_config
                );
            },
            timeout_config
        );
    }

    void readMotorPositionsCanopen(std::function<void(const common::AsyncResult&, const common::MotorPositions&)> callback,
                                  const common::TimeoutConfig& timeout_config) {
        // Read left position
        protocol_->readRegisterAsync(
            motor_constants::canopen::INDEX_POS,
            motor_constants::canopen::SUBINDEX_LEFT,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result, {});
                    }
                    return;
                }

                int32_t left_pos = static_cast<int32_t>(result.data);

                // Read right position
                protocol_->readRegisterAsync(
                    motor_constants::canopen::INDEX_POS,
                    motor_constants::canopen::SUBINDEX_RIGHT,
                    [callback, left_pos](const common::AsyncResult& result2) {
                        if (!result2.isSuccess()) {
                            if (callback) {
                                callback(result2, {});
                            }
                            return;
                        }

                        int32_t right_pos = static_cast<int32_t>(result2.data);
                        common::MotorPositions positions(left_pos, right_pos);
                        
                        if (callback) {
                            callback(common::AsyncResult(common::ResultCode::SUCCESS), positions);
                        }
                    },
                    timeout_config
                );
            },
            timeout_config
        );
    }

    void readMotorSpeedsModbus(std::function<void(const common::AsyncResult&, const common::MotorSpeeds&)> callback,
                              const common::TimeoutConfig& timeout_config) {
        // Read left speed feedback
        protocol_->readRegisterAsync(
            motor_constants::modbus::REG_LEFT_SPEED_FB,
            0,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result, {});
                    }
                    return;
                }

                int16_t left_speed = static_cast<int16_t>(result.data);

                // Read right speed feedback
                protocol_->readRegisterAsync(
                    motor_constants::modbus::REG_RIGHT_SPEED_FB,
                    0,
                    [callback, left_speed](const common::AsyncResult& result2) {
                        if (!result2.isSuccess()) {
                            if (callback) {
                                callback(result2, {});
                            }
                            return;
                        }

                        int16_t right_speed = static_cast<int16_t>(result2.data);
                        
                        // Convert to RPM
                        double left_rpm = static_cast<double>(left_speed);
                        double right_rpm = static_cast<double>(-right_speed);  // Right motor is inverted

                        common::MotorSpeeds speeds(left_rpm, right_rpm);
                        
                        if (callback) {
                            callback(common::AsyncResult(common::ResultCode::SUCCESS), speeds);
                        }
                    },
                    timeout_config
                );
            },
            timeout_config
        );
    }

    void readMotorSpeedsCanopen(std::function<void(const common::AsyncResult&, const common::MotorSpeeds&)> callback,
                               const common::TimeoutConfig& timeout_config) {
        protocol_->readRegisterAsync(
            motor_constants::canopen::INDEX_SPEED,
            motor_constants::canopen::SUBINDEX_L_R,
            [this, callback](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result, {});
                    }
                    return;
                }

                // Extract left and right speeds from combined value
                int16_t left_speed = static_cast<int16_t>(result.data & 0xFFFF);
                int16_t right_speed = static_cast<int16_t>(result.data >> 16);

                // Convert to RPM
                double left_rpm = encoderToRpm(left_speed);
                double right_rpm = encoderToRpm(-right_speed);  // Right motor is inverted

                common::MotorSpeeds speeds(left_rpm, right_rpm);
                
                if (callback) {
                    callback(common::AsyncResult(common::ResultCode::SUCCESS), speeds);
                }
            },
            timeout_config
        );
    }

    void readMotorCurrentsModbus(std::function<void(const common::AsyncResult&, const common::MotorCurrents&)> callback,
                                const common::TimeoutConfig& timeout_config) {
        // Read left current
        protocol_->readRegisterAsync(
            motor_constants::modbus::REG_LEFT_CURRENT,
            0,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result, {});
                    }
                    return;
                }

                int16_t left_current = static_cast<int16_t>(result.data);

                // Read right current
                protocol_->readRegisterAsync(
                    motor_constants::modbus::REG_RIGHT_CURRENT,
                    0,
                    [callback, left_current](const common::AsyncResult& result2) {
                        if (!result2.isSuccess()) {
                            if (callback) {
                                callback(result2, {});
                            }
                            return;
                        }

                        int16_t right_current = static_cast<int16_t>(result2.data);
                        common::MotorCurrents currents(left_current, right_current);
                        
                        if (callback) {
                            callback(common::AsyncResult(common::ResultCode::SUCCESS), currents);
                        }
                    },
                    timeout_config
                );
            },
            timeout_config
        );
    }

    void readMotorCurrentsCanopen(std::function<void(const common::AsyncResult&, const common::MotorCurrents&)> callback,
                                 const common::TimeoutConfig& timeout_config) {
        // Read left current
        protocol_->readRegisterAsync(
            motor_constants::canopen::INDEX_CURRENT_FB,
            motor_constants::canopen::SUBINDEX_CURRENT_FB_LEFT,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result, {});
                    }
                    return;
                }

                int16_t left_current = static_cast<int16_t>(result.data);

                // Read right current
                protocol_->readRegisterAsync(
                    motor_constants::canopen::INDEX_CURRENT_FB,
                    motor_constants::canopen::SUBINDEX_CURRENT_FB_RIGHT,
                    [callback, left_current](const common::AsyncResult& result2) {
                        if (!result2.isSuccess()) {
                            if (callback) {
                                callback(result2, {});
                            }
                            return;
                        }

                        int16_t right_current = static_cast<int16_t>(result2.data);
                        common::MotorCurrents currents(left_current, right_current);
                        
                        if (callback) {
                            callback(common::AsyncResult(common::ResultCode::SUCCESS), currents);
                        }
                    },
                    timeout_config
                );
            },
            timeout_config
        );
    }

    void readMotorTemperaturesModbus(std::function<void(const common::AsyncResult&, const common::MotorTemperatures&)> callback,
                                    const common::TimeoutConfig& timeout_config) {
        // Read motor temperatures register (contains both left and right)
        protocol_->readRegisterAsync(
            motor_constants::modbus::REG_MOTOR_TEMPS,
            0,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result, {});
                    }
                    return;
                }

                // Extract left and right temperatures from combined register
                int16_t left_temp = static_cast<int16_t>(result.data & 0xFF);
                int16_t right_temp = static_cast<int16_t>((result.data >> 8) & 0xFF);

                // Read driver temperature
                protocol_->readRegisterAsync(
                    motor_constants::modbus::REG_DRIVER_TEMP,
                    0,
                    [callback, left_temp, right_temp](const common::AsyncResult& result2) {
                        if (!result2.isSuccess()) {
                            if (callback) {
                                callback(result2, {});
                            }
                            return;
                        }

                        int16_t driver_temp = static_cast<int16_t>(result2.data);
                        common::MotorTemperatures temps(left_temp, right_temp, driver_temp);
                        
                        if (callback) {
                            callback(common::AsyncResult(common::ResultCode::SUCCESS), temps);
                        }
                    },
                    timeout_config
                );
            },
            timeout_config
        );
    }

    void readMotorTemperaturesCanopen(std::function<void(const common::AsyncResult&, const common::MotorTemperatures&)> callback,
                                     const common::TimeoutConfig& timeout_config) {
        // Read left temperature
        protocol_->readRegisterAsync(
            motor_constants::canopen::INDEX_TEMPERATURE,
            motor_constants::canopen::SUBINDEX_TEMPERATURE_LEFT,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result, {});
                    }
                    return;
                }

                int16_t left_temp = static_cast<int16_t>(result.data);

                // Read right temperature
                protocol_->readRegisterAsync(
                    motor_constants::canopen::INDEX_TEMPERATURE,
                    motor_constants::canopen::SUBINDEX_TEMPERATURE_RIGHT,
                    [this, callback, timeout_config, left_temp](const common::AsyncResult& result2) {
                        if (!result2.isSuccess()) {
                            if (callback) {
                                callback(result2, {});
                            }
                            return;
                        }

                        int16_t right_temp = static_cast<int16_t>(result2.data);

                        // Read driver temperature
                        protocol_->readRegisterAsync(
                            motor_constants::canopen::INDEX_TEMPERATURE,
                            motor_constants::canopen::SUBINDEX_TEMPERATURE_DRIVER,
                            [callback, left_temp, right_temp](const common::AsyncResult& result3) {
                                if (!result3.isSuccess()) {
                                    if (callback) {
                                        callback(result3, {});
                                    }
                                    return;
                                }

                                int16_t driver_temp = static_cast<int16_t>(result3.data);
                                common::MotorTemperatures temps(left_temp, right_temp, driver_temp);
                                
                                if (callback) {
                                    callback(common::AsyncResult(common::ResultCode::SUCCESS), temps);
                                }
                            },
                            timeout_config
                        );
                    },
                    timeout_config
                );
            },
            timeout_config
        );
    }

    int32_t rpmToEncoderSpeed(double rpm) {
        // Convert RPM to encoder speed based on robot parameters
        double rad_per_sec = rpm * M_PI / 30.0;
        double encoder_speed = rad_per_sec * robot_params_.encoder_resolution / (2.0 * M_PI);
        return static_cast<int32_t>(encoder_speed);
    }

    double encoderToRpm(int32_t encoder_speed) {
        // Convert encoder speed to RPM
        double rad_per_sec = encoder_speed * 2.0 * M_PI / robot_params_.encoder_resolution;
        return rad_per_sec * 30.0 / M_PI;
    }

    std::shared_ptr<protocol::ICommunicationProtocol> protocol_;
    common::RobotParameters robot_params_;
    std::atomic<bool> initialized_;
    std::atomic<bool> motors_enabled_;
    int emergency_stop_trigger_level_;
    std::string protocol_type_;
    bool is_modbus_;
};

// MotorController implementation
MotorController::MotorController() : pImpl_(std::make_unique<Impl>()) {}

MotorController::~MotorController() = default;

bool MotorController::initialize(std::shared_ptr<protocol::ICommunicationProtocol> protocol,
                                const common::RobotParameters& params) {
    return pImpl_->initialize(protocol, params);
}

void MotorController::shutdown() {
    pImpl_->shutdown();
}

bool MotorController::isReady() const {
    return pImpl_->isReady();
}

void MotorController::enableMotors(common::AsyncCallback callback,
                                  const common::TimeoutConfig& timeout_config) {
    pImpl_->enableMotors(callback, timeout_config);
}

void MotorController::disableMotors(common::AsyncCallback callback,
                                   const common::TimeoutConfig& timeout_config) {
    pImpl_->disableMotors(callback, timeout_config);
}

void MotorController::setMotorSpeeds(const common::MotorSpeeds& speeds,
                                    common::AsyncCallback callback,
                                    const common::TimeoutConfig& timeout_config) {
    pImpl_->setMotorSpeeds(speeds, callback, timeout_config);
}

void MotorController::readMotorPositions(std::function<void(const common::AsyncResult&, const common::MotorPositions&)> callback,
                                        const common::TimeoutConfig& timeout_config) {
    pImpl_->readMotorPositions(callback, timeout_config);
}

void MotorController::readMotorSpeeds(std::function<void(const common::AsyncResult&, const common::MotorSpeeds&)> callback,
                                     const common::TimeoutConfig& timeout_config) {
    pImpl_->readMotorSpeeds(callback, timeout_config);
}

void MotorController::readMotorCurrents(std::function<void(const common::AsyncResult&, const common::MotorCurrents&)> callback,
                                       const common::TimeoutConfig& timeout_config) {
    pImpl_->readMotorCurrents(callback, timeout_config);
}

void MotorController::readMotorTemperatures(std::function<void(const common::AsyncResult&, const common::MotorTemperatures&)> callback,
                                           const common::TimeoutConfig& timeout_config) {
    pImpl_->readMotorTemperatures(callback, timeout_config);
}

void MotorController::readGpioStatus(std::function<void(const common::AsyncResult&, const common::GpioStatus&)> callback,
                                    const common::TimeoutConfig& timeout_config) {
    pImpl_->readGpioStatus(callback, timeout_config);
}

void MotorController::readLastAlarm(std::function<void(const common::AsyncResult&, uint32_t)> callback,
                                   const common::TimeoutConfig& timeout_config) {
    pImpl_->readLastAlarm(callback, timeout_config);
}

void MotorController::clearAlarm(common::AsyncCallback callback,
                                const common::TimeoutConfig& timeout_config) {
    pImpl_->clearAlarm(callback, timeout_config);
}

std::string MotorController::getDeviceType() const {
    return pImpl_->getDeviceType();
}

bool MotorController::areMotorsEnabled() const {
    return pImpl_->areMotorsEnabled();
}

void MotorController::setEmergencyStopTriggerLevel(int level) {
    pImpl_->setEmergencyStopTriggerLevel(level);
}

} // namespace device
} // namespace sl_pmu
