#include "sl_pmu/device/motor_controller.hpp"
#include <atomic>

namespace sl_pmu {
namespace device {

class PowerController::Impl {
public:
    Impl() : initialized_(false) {}

    ~Impl() {
        shutdown();
    }

    bool initialize(std::shared_ptr<protocol::ICommunicationProtocol> protocol) {
        if (initialized_) {
            return true;
        }

        protocol_ = protocol;

        if (!protocol_ || !protocol_->isReady()) {
            return false;
        }

        // Determine protocol type
        protocol_type_ = protocol_->getProtocolType();
        is_modbus_ = (protocol_type_.find("Modbus") != std::string::npos);

        initialized_ = true;
        return true;
    }

    void shutdown() {
        if (!initialized_) {
            return;
        }

        protocol_.reset();
        initialized_ = false;
    }

    bool isReady() const {
        return initialized_ && protocol_ && protocol_->isReady();
    }

    void controlPowerButtonLight(bool enable,
                                common::AsyncCallback callback,
                                const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"));
            }
            return;
        }

        uint32_t gpio_value = enable ? motor_constants::GPIO_O_POWER_OFF_BUTTON_LIGHT : 0;

        if (is_modbus_) {
            // For Modbus, we would need to read-modify-write the GPIO output register
            // This is a simplified implementation
            protocol_->writeRegisterAsync(
                motor_constants::modbus::REG_GPIO_STATUS,  // This might need a different register for outputs
                0,
                gpio_value,
                callback,
                timeout_config
            );
        } else {
            protocol_->writeRegisterAsync(
                motor_constants::canopen::INDEX_DOUT_CONTROL,
                motor_constants::canopen::SUBINDEX_DOUT_CONTROL,
                gpio_value,
                callback,
                timeout_config
            );
        }
    }

    void controlMainPower(bool enable,
                         common::AsyncCallback callback,
                         const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"));
            }
            return;
        }

        uint32_t gpio_value = enable ? motor_constants::GPIO_O_POWER_CONTROL : 0;

        if (is_modbus_) {
            // For Modbus, we would need to read-modify-write the GPIO output register
            // This is a simplified implementation
            protocol_->writeRegisterAsync(
                motor_constants::modbus::REG_GPIO_STATUS,  // This might need a different register for outputs
                0,
                gpio_value,
                callback,
                timeout_config
            );
        } else {
            protocol_->writeRegisterAsync(
                motor_constants::canopen::INDEX_DOUT_CONTROL,
                motor_constants::canopen::SUBINDEX_DOUT_CONTROL,
                gpio_value,
                callback,
                timeout_config
            );
        }
    }

    std::string getDeviceType() const {
        return "Power Controller (" + protocol_type_ + ")";
    }

private:
    std::shared_ptr<protocol::ICommunicationProtocol> protocol_;
    std::atomic<bool> initialized_;
    std::string protocol_type_;
    bool is_modbus_;
};

PowerController::PowerController() : pImpl_(std::make_unique<Impl>()) {}

PowerController::~PowerController() = default;

bool PowerController::initialize(std::shared_ptr<protocol::ICommunicationProtocol> protocol) {
    return pImpl_->initialize(protocol);
}

void PowerController::shutdown() {
    pImpl_->shutdown();
}

bool PowerController::isReady() const {
    return pImpl_->isReady();
}

void PowerController::controlPowerButtonLight(bool enable,
                                             common::AsyncCallback callback,
                                             const common::TimeoutConfig& timeout_config) {
    pImpl_->controlPowerButtonLight(enable, callback, timeout_config);
}

void PowerController::controlMainPower(bool enable,
                                      common::AsyncCallback callback,
                                      const common::TimeoutConfig& timeout_config) {
    pImpl_->controlMainPower(enable, callback, timeout_config);
}

std::string PowerController::getDeviceType() const {
    return pImpl_->getDeviceType();
}

// DeviceFactory implementation
std::unique_ptr<IMotorController> DeviceFactory::createMotorController() {
    return std::make_unique<MotorController>();
}

std::unique_ptr<IPowerController> DeviceFactory::createPowerController() {
    return std::make_unique<PowerController>();
}

} // namespace device
} // namespace sl_pmu
