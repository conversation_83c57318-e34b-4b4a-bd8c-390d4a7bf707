#include "sl_pmu/common/types.hpp"
#include <map>
#include <string>

namespace sl_pmu {
namespace common {

// Configuration keys
namespace config_keys {
    constexpr const char* DRIVER_TYPE = "driver_type";
    constexpr const char* DRIVER_CONFIG = "driver_config";
    constexpr const char* PROTOCOL_TYPE = "protocol_type";
    constexpr const char* PROTOCOL_NODE_ID = "protocol_node_id";
    
    constexpr const char* MOTOR_DEVICE_TYPE = "motor_device_type";
    constexpr const char* POWER_DEVICE_TYPE = "power_device_type";
    
    constexpr const char* WHEEL_DIAMETER_LEFT = "wheel_diameter_left";
    constexpr const char* WHEEL_DIAMETER_RIGHT = "wheel_diameter_right";
    constexpr const char* WHEEL_SEPARATION = "wheel_separation";
    constexpr const char* GEAR_RATIO = "gear_ratio";
    constexpr const char* ENCODER_RESOLUTION = "encoder_resolution";
    
    constexpr const char* ODOM_FRAME_ID = "odom_frame_id";
    constexpr const char* BASE_FRAME_ID = "base_frame_id";
    constexpr const char* PUBLISH_TF = "publish_tf";
    
    constexpr const char* CMD_VEL_TOPIC = "cmd_vel_topic";
    constexpr const char* ODOM_TOPIC = "odom_topic";
    constexpr const char* FILTERED_ODOM_TOPIC = "filtered_odom_topic";
    constexpr const char* JOINT_STATE_TOPIC = "joint_state_topic";
    constexpr const char* MOTOR_INFO_TOPIC = "motor_info_topic";
    constexpr const char* MOTOR_STATE_TOPIC = "motor_state_topic";
    constexpr const char* BUMPER_TOPIC = "bumper_topic";
    constexpr const char* POWER_OFF_TOPIC = "power_off_topic";
    constexpr const char* FILTERED_IMU_TOPIC = "filtered_imu_topic";
    
    constexpr const char* PUBLISH_MOTOR_INFO = "publish_motor_info";
    constexpr const char* PRINT_STATUS_OUT = "print_status_out";
    
    constexpr const char* CONTROL_CYCLE_MS = "control_cycle_ms";
    constexpr const char* STATUS_UPDATE_CYCLE_MS = "status_update_cycle_ms";
    
    constexpr const char* CMD_VEL_TIMEOUT_MS = "cmd_vel_timeout_ms";
    constexpr const char* BUMPER_TIMEOUT_MS = "bumper_timeout_ms";
    constexpr const char* EMERGENCY_STOP_BIT0_TRIGGER_LEVEL = "emergency_stop_bit0_trigger_level";
    constexpr const char* POWER_OFF_TIMEOUT_MS = "power_off_timeout_ms";
    constexpr const char* POWER_OFF_PUBLISH_PERIOD_MS = "power_off_publish_period_ms";
}

} // namespace common
} // namespace sl_pmu
