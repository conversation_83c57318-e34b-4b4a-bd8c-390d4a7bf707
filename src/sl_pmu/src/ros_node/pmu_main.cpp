#include <rclcpp/rclcpp.hpp>
#include "sl_pmu/ros_node/pmu_ros_node.hpp"
#include <memory>

int main(int argc, char** argv) {
    rclcpp::init(argc, argv);
    
    try {
        auto node = std::make_shared<sl_pmu::ros_node::PmuRosNode>();
        
        RCLCPP_INFO(node->get_logger(), "PMU Node started, spinning...");
        rclcpp::spin(node);
        
        RCLCPP_INFO(node->get_logger(), "PMU Node shutting down");
    } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("pmu_main"), "Exception in main: %s", e.what());
        return 1;
    }
    
    rclcpp::shutdown();
    return 0;
}
