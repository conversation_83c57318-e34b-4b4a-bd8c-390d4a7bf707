#include "sl_pmu/application/pmu_application.hpp"
#include <mutex>
#include <atomic>
#include <thread>
#include <cmath>

namespace sl_pmu {
namespace application {

class PmuApplication::Impl {
public:
    Impl() : state_(ApplicationState::INITIALIZING), running_(false) {}

    ~Impl() {
        shutdown();
    }

    bool initialize(const PmuConfig& config,
                   std::shared_ptr<device::IMotorController> motor_controller,
                   std::shared_ptr<device::IPowerController> power_controller) {
        if (state_ != ApplicationState::INITIALIZING) {
            return false;
        }

        config_ = config;
        motor_controller_ = motor_controller;
        power_controller_ = power_controller;

        if (!motor_controller_ || !motor_controller_->isReady()) {
            state_ = ApplicationState::ERROR;
            return false;
        }

        // Initialize state
        latest_cmd_vel_ = VelocityCommand();
        latest_bumper_state_ = BumperState();
        current_odometry_ = OdometryData();
        current_motor_status_ = MotorStatus();
        current_power_status_ = common::PowerStatus();

        // Start update thread
        running_ = true;
        update_thread_ = std::thread(&Impl::updateThreadFunction, this);

        state_ = ApplicationState::READY;
        return true;
    }

    void shutdown() {
        if (state_ == ApplicationState::SHUTDOWN) {
            return;
        }

        state_ = ApplicationState::SHUTDOWN;
        running_ = false;

        if (update_thread_.joinable()) {
            update_thread_.join();
        }

        // Disable motors
        if (motor_controller_ && motor_controller_->isReady()) {
            motor_controller_->disableMotors([](const common::AsyncResult&) {}, {});
        }

        motor_controller_.reset();
        power_controller_.reset();
    }

    ApplicationState getState() const {
        return state_;
    }

    void processVelocityCommand(const VelocityCommand& cmd) {
        std::lock_guard<std::mutex> lock(state_mutex_);
        latest_cmd_vel_ = cmd;
    }

    void processBumperState(const BumperState& state) {
        std::lock_guard<std::mutex> lock(state_mutex_);
        latest_bumper_state_ = state;
    }

    void requestAlarmClear() {
        if (!motor_controller_ || !motor_controller_->isReady()) {
            return;
        }

        motor_controller_->clearAlarm([this](const common::AsyncResult& result) {
            if (result.isSuccess()) {
                // Restart motor initialization after alarm clear
                state_ = ApplicationState::READY;
            } else {
                if (error_callback_) {
                    error_callback_("Failed to clear alarm: " + result.message);
                }
            }
        });
    }

    void update() {
        if (state_ == ApplicationState::SHUTDOWN) {
            return;
        }

        auto now = std::chrono::steady_clock::now();

        // Check timeouts
        checkTimeouts(now);

        // Update motor control based on state
        switch (state_) {
            case ApplicationState::READY:
                // Try to enable motors
                enableMotors();
                break;

            case ApplicationState::RUNNING:
                // Process velocity commands and update motor speeds
                processMotorControl(now);
                break;

            case ApplicationState::ERROR:
                // Try to recover after some time
                if (std::chrono::duration_cast<std::chrono::seconds>(now - last_error_time_).count() > 5) {
                    state_ = ApplicationState::READY;
                }
                break;

            default:
                break;
        }

        // Update status periodically
        if (std::chrono::duration_cast<std::chrono::milliseconds>(now - last_status_update_).count() 
            >= config_.status_update_interval.count()) {
            updateMotorStatus();
            last_status_update_ = now;
        }

        // Update power control
        updatePowerControl(now);
    }

    OdometryData getOdometryData() const {
        std::lock_guard<std::mutex> lock(state_mutex_);
        return current_odometry_;
    }

    MotorStatus getMotorStatus() const {
        std::lock_guard<std::mutex> lock(state_mutex_);
        return current_motor_status_;
    }

    common::PowerStatus getPowerStatus() const {
        std::lock_guard<std::mutex> lock(state_mutex_);
        return current_power_status_;
    }

    void setStatusUpdateCallback(std::function<void(const MotorStatus&)> callback) {
        status_update_callback_ = callback;
    }

    void setOdometryUpdateCallback(std::function<void(const OdometryData&)> callback) {
        odometry_update_callback_ = callback;
    }

    void setPowerStatusUpdateCallback(std::function<void(const common::PowerStatus&)> callback) {
        power_status_update_callback_ = callback;
    }

    void setErrorCallback(std::function<void(const std::string&)> callback) {
        error_callback_ = callback;
    }

private:
    void updateThreadFunction() {
        while (running_) {
            update();
            std::this_thread::sleep_for(std::chrono::milliseconds(20));  // 50Hz update rate
        }
    }

    void checkTimeouts(std::chrono::steady_clock::time_point now) {
        std::lock_guard<std::mutex> lock(state_mutex_);

        // Check cmd_vel timeout
        auto cmd_vel_elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            now - latest_cmd_vel_.timestamp);
        if (cmd_vel_elapsed > config_.cmd_vel_timeout) {
            // Set zero velocity
            latest_cmd_vel_.linear_x = 0.0;
            latest_cmd_vel_.angular_z = 0.0;
        }

        // Check bumper timeout
        auto bumper_elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            now - latest_bumper_state_.timestamp);
        if (bumper_elapsed > config_.bumper_timeout) {
            // Assume safe state
            latest_bumper_state_.front_bumper_triggered = false;
            latest_bumper_state_.back_bumper_triggered = false;
        }
    }

    void enableMotors() {
        if (!motor_controller_ || !motor_controller_->isReady()) {
            return;
        }

        motor_controller_->enableMotors([this](const common::AsyncResult& result) {
            if (result.isSuccess()) {
                state_ = ApplicationState::RUNNING;
            } else {
                state_ = ApplicationState::ERROR;
                last_error_time_ = std::chrono::steady_clock::now();
                if (error_callback_) {
                    error_callback_("Failed to enable motors: " + result.message);
                }
            }
        });
    }

    void processMotorControl(std::chrono::steady_clock::time_point now) {
        if (!motor_controller_ || !motor_controller_->isReady()) {
            return;
        }

        VelocityCommand cmd_vel;
        BumperState bumper_state;
        
        {
            std::lock_guard<std::mutex> lock(state_mutex_);
            cmd_vel = latest_cmd_vel_;
            bumper_state = latest_bumper_state_;
        }

        // Convert twist to differential drive wheel speeds
        double linear_velocity = cmd_vel.linear_x;  // m/s
        double angular_velocity = cmd_vel.angular_z;  // rad/s

        // Calculate wheel velocities using differential drive kinematics
        double left_wheel_linear = linear_velocity - (angular_velocity * config_.robot_params.wheel_separation / 2.0);
        double right_wheel_linear = linear_velocity + (angular_velocity * config_.robot_params.wheel_separation / 2.0);

        // Convert linear velocity to angular velocity (rad/s)
        double left_wheel_angular = left_wheel_linear / (config_.robot_params.wheel_diameter_left / 2.0);
        double right_wheel_angular = right_wheel_linear / (config_.robot_params.wheel_diameter_right / 2.0);

        // Convert to RPM
        double left_rpm = left_wheel_angular * 30.0 / M_PI;
        double right_rpm = right_wheel_angular * 30.0 / M_PI;

        // Apply safety constraints
        applySafetyConstraints(left_rpm, right_rpm, bumper_state);

        // Set motor speeds
        common::MotorSpeeds speeds(left_rpm, right_rpm);
        motor_controller_->setMotorSpeeds(speeds, [this](const common::AsyncResult& result) {
            if (!result.isSuccess()) {
                state_ = ApplicationState::ERROR;
                last_error_time_ = std::chrono::steady_clock::now();
                if (error_callback_) {
                    error_callback_("Failed to set motor speeds: " + result.message);
                }
            }
        });

        // Update odometry
        updateOdometry(now);
    }

    void applySafetyConstraints(double& left_rpm, double& right_rpm, const BumperState& bumper_state) {
        // Emergency stop and brake release checks would go here
        // For now, just implement bumper logic

        if (bumper_state.front_bumper_triggered) {
            // Stop forward movement
            if (left_rpm > 0) left_rpm = 0;
            if (right_rpm > 0) right_rpm = 0;
        }

        if (bumper_state.back_bumper_triggered) {
            // Stop backward movement
            if (left_rpm < 0) left_rpm = 0;
            if (right_rpm < 0) right_rpm = 0;
        }
    }

    void updateMotorStatus() {
        if (!motor_controller_ || !motor_controller_->isReady()) {
            return;
        }

        // Read motor positions
        motor_controller_->readMotorPositions([this](const common::AsyncResult& result, const common::MotorPositions& positions) {
            if (result.isSuccess()) {
                std::lock_guard<std::mutex> lock(state_mutex_);
                current_motor_status_.positions = positions;
                current_motor_status_.timestamp = std::chrono::steady_clock::now();
            }
        });

        // Read motor currents
        motor_controller_->readMotorCurrents([this](const common::AsyncResult& result, const common::MotorCurrents& currents) {
            if (result.isSuccess()) {
                std::lock_guard<std::mutex> lock(state_mutex_);
                current_motor_status_.currents = currents;
            }
        });

        // Read motor temperatures
        motor_controller_->readMotorTemperatures([this](const common::AsyncResult& result, const common::MotorTemperatures& temperatures) {
            if (result.isSuccess()) {
                std::lock_guard<std::mutex> lock(state_mutex_);
                current_motor_status_.temperatures = temperatures;
            }
        });

        // Read GPIO status
        motor_controller_->readGpioStatus([this](const common::AsyncResult& result, const common::GpioStatus& gpio_status) {
            if (result.isSuccess()) {
                std::lock_guard<std::mutex> lock(state_mutex_);
                current_motor_status_.gpio_status = gpio_status;
            }
        });

        // Read last alarm
        motor_controller_->readLastAlarm([this](const common::AsyncResult& result, uint32_t alarm_code) {
            if (result.isSuccess()) {
                std::lock_guard<std::mutex> lock(state_mutex_);
                current_motor_status_.last_alarm = alarm_code;
                
                if (alarm_code != 0) {
                    state_ = ApplicationState::ERROR;
                    last_error_time_ = std::chrono::steady_clock::now();
                }
            }
        });

        // Update motor enabled status
        {
            std::lock_guard<std::mutex> lock(state_mutex_);
            current_motor_status_.motors_enabled = motor_controller_->areMotorsEnabled();
        }

        // Call status update callback
        if (status_update_callback_) {
            status_update_callback_(current_motor_status_);
        }
    }

    void updateOdometry(std::chrono::steady_clock::time_point now) {
        // Simplified odometry update - in a real implementation, you would
        // integrate wheel encoder data to calculate position and orientation
        
        std::lock_guard<std::mutex> lock(state_mutex_);
        current_odometry_.timestamp = now;
        
        // Call odometry update callback
        if (odometry_update_callback_) {
            odometry_update_callback_(current_odometry_);
        }
    }

    void updatePowerControl(std::chrono::steady_clock::time_point now) {
        // Check for power off button press in GPIO status
        bool power_off_pressed = current_motor_status_.gpio_status.isPowerOffPressed();
        
        if (power_off_pressed && !current_power_status_.power_off_requested) {
            // Start power off sequence
            std::lock_guard<std::mutex> lock(state_mutex_);
            current_power_status_.power_off_requested = true;
            current_power_status_.request_time = now;
            
            if (power_controller_ && power_controller_->isReady()) {
                // Turn on power button light
                power_controller_->controlPowerButtonLight(true, [](const common::AsyncResult&) {}, {});
            }
        }

        // Check power off timeout
        if (current_power_status_.power_off_requested) {
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                now - current_power_status_.request_time);
            
            if (elapsed > config_.power_off_timeout) {
                // Execute power off
                if (power_controller_ && power_controller_->isReady()) {
                    power_controller_->controlMainPower(false, [](const common::AsyncResult&) {}, {});
                }
            }
        }

        // Call power status update callback
        if (power_status_update_callback_) {
            power_status_update_callback_(current_power_status_);
        }
    }

    // Configuration and devices
    PmuConfig config_;
    std::shared_ptr<device::IMotorController> motor_controller_;
    std::shared_ptr<device::IPowerController> power_controller_;

    // State
    std::atomic<ApplicationState> state_;
    std::atomic<bool> running_;
    std::thread update_thread_;
    mutable std::mutex state_mutex_;

    // Application data
    VelocityCommand latest_cmd_vel_;
    BumperState latest_bumper_state_;
    OdometryData current_odometry_;
    MotorStatus current_motor_status_;
    common::PowerStatus current_power_status_;

    // Timing
    std::chrono::steady_clock::time_point last_status_update_;
    std::chrono::steady_clock::time_point last_error_time_;

    // Callbacks
    std::function<void(const MotorStatus&)> status_update_callback_;
    std::function<void(const OdometryData&)> odometry_update_callback_;
    std::function<void(const common::PowerStatus&)> power_status_update_callback_;
    std::function<void(const std::string&)> error_callback_;
};

// PmuApplication implementation
PmuApplication::PmuApplication() : pImpl_(std::make_unique<Impl>()) {}

PmuApplication::~PmuApplication() = default;

bool PmuApplication::initialize(const PmuConfig& config,
                               std::shared_ptr<device::IMotorController> motor_controller,
                               std::shared_ptr<device::IPowerController> power_controller) {
    return pImpl_->initialize(config, motor_controller, power_controller);
}

void PmuApplication::shutdown() {
    pImpl_->shutdown();
}

ApplicationState PmuApplication::getState() const {
    return pImpl_->getState();
}

void PmuApplication::processVelocityCommand(const VelocityCommand& cmd) {
    pImpl_->processVelocityCommand(cmd);
}

void PmuApplication::processBumperState(const BumperState& state) {
    pImpl_->processBumperState(state);
}

void PmuApplication::requestAlarmClear() {
    pImpl_->requestAlarmClear();
}

void PmuApplication::update() {
    pImpl_->update();
}

OdometryData PmuApplication::getOdometryData() const {
    return pImpl_->getOdometryData();
}

MotorStatus PmuApplication::getMotorStatus() const {
    return pImpl_->getMotorStatus();
}

common::PowerStatus PmuApplication::getPowerStatus() const {
    return pImpl_->getPowerStatus();
}

void PmuApplication::setStatusUpdateCallback(std::function<void(const MotorStatus&)> callback) {
    pImpl_->setStatusUpdateCallback(callback);
}

void PmuApplication::setOdometryUpdateCallback(std::function<void(const OdometryData&)> callback) {
    pImpl_->setOdometryUpdateCallback(callback);
}

void PmuApplication::setPowerStatusUpdateCallback(std::function<void(const common::PowerStatus&)> callback) {
    pImpl_->setPowerStatusUpdateCallback(callback);
}

void PmuApplication::setErrorCallback(std::function<void(const std::string&)> callback) {
    pImpl_->setErrorCallback(callback);
}

} // namespace application
} // namespace sl_pmu
