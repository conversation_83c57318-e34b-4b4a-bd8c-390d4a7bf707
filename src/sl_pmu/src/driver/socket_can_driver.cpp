#include "sl_pmu/driver/communication_driver.hpp"
#include <linux/can.h>
#include <linux/can/raw.h>
#include <net/if.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <unistd.h>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <chrono>
#include <cstring>

namespace sl_pmu {
namespace driver {

class SocketCanDriver::Impl {
public:
    Impl() : socket_fd_(-1), running_(false), initialized_(false) {}
    
    ~Impl() {
        shutdown();
    }

    bool initialize(const std::string& interface_name) {
        if (initialized_) {
            return true;
        }

        interface_name_ = interface_name;
        
        // Create socket
        socket_fd_ = socket(PF_CAN, SOCK_RAW, CAN_RAW);
        if (socket_fd_ < 0) {
            return false;
        }

        // Get interface index
        struct ifreq ifr;
        strcpy(ifr.ifr_name, interface_name.c_str());
        if (ioctl(socket_fd_, SIOCGIFINDEX, &ifr) < 0) {
            close(socket_fd_);
            socket_fd_ = -1;
            return false;
        }

        // Bind socket to CAN interface
        struct sockaddr_can addr;
        memset(&addr, 0, sizeof(addr));
        addr.can_family = AF_CAN;
        addr.can_ifindex = ifr.ifr_ifindex;

        if (bind(socket_fd_, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
            close(socket_fd_);
            socket_fd_ = -1;
            return false;
        }

        // Start receive thread
        running_ = true;
        receive_thread_ = std::thread(&Impl::receiveThreadFunction, this);
        send_thread_ = std::thread(&Impl::sendThreadFunction, this);
        
        initialized_ = true;
        return true;
    }

    void shutdown() {
        if (!initialized_) {
            return;
        }

        running_ = false;
        
        // Notify send thread
        {
            std::lock_guard<std::mutex> lock(send_queue_mutex_);
            send_queue_cv_.notify_all();
        }

        if (receive_thread_.joinable()) {
            receive_thread_.join();
        }
        
        if (send_thread_.joinable()) {
            send_thread_.join();
        }

        if (socket_fd_ >= 0) {
            close(socket_fd_);
            socket_fd_ = -1;
        }

        initialized_ = false;
    }

    bool isReady() const {
        return initialized_ && socket_fd_ >= 0;
    }

    common::AsyncResult sendFrame(const common::CommFrame& frame) {
        if (!isReady()) {
            return common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Driver not ready");
        }

        struct can_frame can_frame;
        memset(&can_frame, 0, sizeof(can_frame));
        
        can_frame.can_id = frame.id;
        if (frame.is_extended) {
            can_frame.can_id |= CAN_EFF_FLAG;
        }
        if (frame.is_rtr) {
            can_frame.can_id |= CAN_RTR_FLAG;
        }
        
        can_frame.can_dlc = frame.length;
        memcpy(can_frame.data, frame.data, std::min(static_cast<size_t>(frame.length), sizeof(can_frame.data)));

        ssize_t bytes_sent = write(socket_fd_, &can_frame, sizeof(can_frame));
        if (bytes_sent != sizeof(can_frame)) {
            return common::AsyncResult(common::ResultCode::COMMUNICATION_ERROR, "Failed to send CAN frame");
        }

        return common::AsyncResult(common::ResultCode::SUCCESS);
    }

    void sendFrameAsync(const common::CommFrame& frame, 
                       common::AsyncCallback callback,
                       const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Driver not ready"));
            }
            return;
        }

        // Queue frame for sending
        {
            std::lock_guard<std::mutex> lock(send_queue_mutex_);
            SendRequest request;
            request.frame = frame;
            request.callback = callback;
            request.timeout = timeout_config.timeout;
            request.timestamp = std::chrono::steady_clock::now();
            send_queue_.push(request);
        }
        send_queue_cv_.notify_one();
    }

    void setReceiveCallback(std::function<void(const common::CommFrame&)> callback) {
        std::lock_guard<std::mutex> lock(receive_callback_mutex_);
        receive_callback_ = callback;
    }

    std::string getDriverType() const {
        return "SocketCAN";
    }

    std::string getConfiguration() const {
        return interface_name_;
    }

private:
    struct SendRequest {
        common::CommFrame frame;
        common::AsyncCallback callback;
        std::chrono::milliseconds timeout;
        std::chrono::steady_clock::time_point timestamp;
    };

    void receiveThreadFunction() {
        while (running_) {
            struct can_frame can_frame;
            ssize_t bytes_read = read(socket_fd_, &can_frame, sizeof(can_frame));
            
            if (bytes_read == sizeof(can_frame)) {
                common::CommFrame frame;
                frame.id = can_frame.can_id & CAN_EFF_MASK;
                frame.is_extended = (can_frame.can_id & CAN_EFF_FLAG) != 0;
                frame.is_rtr = (can_frame.can_id & CAN_RTR_FLAG) != 0;
                frame.length = can_frame.can_dlc;
                memcpy(frame.data, can_frame.data, std::min(static_cast<size_t>(can_frame.can_dlc), sizeof(frame.data)));

                std::lock_guard<std::mutex> lock(receive_callback_mutex_);
                if (receive_callback_) {
                    receive_callback_(frame);
                }
            } else if (bytes_read < 0 && running_) {
                // Error occurred, but only log if we're still supposed to be running
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }

    void sendThreadFunction() {
        while (running_) {
            std::unique_lock<std::mutex> lock(send_queue_mutex_);
            send_queue_cv_.wait(lock, [this] { return !send_queue_.empty() || !running_; });

            if (!running_) {
                break;
            }

            if (!send_queue_.empty()) {
                SendRequest request = send_queue_.front();
                send_queue_.pop();
                lock.unlock();

                // Check timeout
                auto now = std::chrono::steady_clock::now();
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - request.timestamp);
                
                if (elapsed > request.timeout) {
                    if (request.callback) {
                        request.callback(common::AsyncResult(common::ResultCode::TIMEOUT, "Send timeout"));
                    }
                    continue;
                }

                // Send frame
                auto result = sendFrame(request.frame);
                if (request.callback) {
                    request.callback(result);
                }
            }
        }
    }

    int socket_fd_;
    std::string interface_name_;
    std::atomic<bool> running_;
    std::atomic<bool> initialized_;
    
    std::thread receive_thread_;
    std::thread send_thread_;
    
    std::queue<SendRequest> send_queue_;
    std::mutex send_queue_mutex_;
    std::condition_variable send_queue_cv_;
    
    std::function<void(const common::CommFrame&)> receive_callback_;
    std::mutex receive_callback_mutex_;
};

// SocketCanDriver implementation
SocketCanDriver::SocketCanDriver() : pImpl_(std::make_unique<Impl>()) {}

SocketCanDriver::~SocketCanDriver() = default;

bool SocketCanDriver::initialize(const std::string& config) {
    return pImpl_->initialize(config);
}

void SocketCanDriver::shutdown() {
    pImpl_->shutdown();
}

bool SocketCanDriver::isReady() const {
    return pImpl_->isReady();
}

common::AsyncResult SocketCanDriver::sendFrame(const common::CommFrame& frame) {
    return pImpl_->sendFrame(frame);
}

void SocketCanDriver::sendFrameAsync(const common::CommFrame& frame, 
                                    common::AsyncCallback callback,
                                    const common::TimeoutConfig& timeout_config) {
    pImpl_->sendFrameAsync(frame, callback, timeout_config);
}

void SocketCanDriver::setReceiveCallback(std::function<void(const common::CommFrame&)> callback) {
    pImpl_->setReceiveCallback(callback);
}

std::string SocketCanDriver::getDriverType() const {
    return pImpl_->getDriverType();
}

std::string SocketCanDriver::getConfiguration() const {
    return pImpl_->getConfiguration();
}

} // namespace driver
} // namespace sl_pmu
