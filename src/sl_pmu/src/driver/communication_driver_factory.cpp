#include "sl_pmu/driver/communication_driver.hpp"
#include <memory>
#include <string>
#include <algorithm>

namespace sl_pmu {
namespace driver {

/**
 * @brief Mock driver for testing purposes
 */
class MockDriver : public ICommunicationDriver {
public:
    MockDriver() : initialized_(false) {}
    
    bool initialize(const std::string& config) override {
        config_ = config;
        initialized_ = true;
        return true;
    }
    
    void shutdown() override {
        initialized_ = false;
    }
    
    bool isReady() const override {
        return initialized_;
    }
    
    common::AsyncResult sendFrame(const common::CommFrame& frame) override {
        (void)frame;  // Suppress unused parameter warning
        if (!initialized_) {
            return common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Mock driver not ready");
        }
        return common::AsyncResult(common::ResultCode::SUCCESS);
    }
    
    void sendFrameAsync(const common::CommFrame& frame, 
                       common::AsyncCallback callback,
                       const common::TimeoutConfig& timeout_config) override {
        (void)timeout_config;  // Suppress unused parameter warning
        auto result = sendFrame(frame);
        if (callback) {
            callback(result);
        }
    }
    
    void setReceiveCallback(std::function<void(const common::CommFrame&)> callback) override {
        receive_callback_ = callback;
    }
    
    std::string getDriverType() const override {
        return "Mock";
    }
    
    std::string getConfiguration() const override {
        return config_;
    }
    
    // Test helper method to simulate receiving a frame
    void simulateReceive(const common::CommFrame& frame) {
        if (receive_callback_) {
            receive_callback_(frame);
        }
    }

private:
    bool initialized_;
    std::string config_;
    std::function<void(const common::CommFrame&)> receive_callback_;
};

// Factory implementation
std::unique_ptr<ICommunicationDriver> CommunicationDriverFactory::createDriver(DriverType type) {
    switch (type) {
        case DriverType::SOCKET_CAN:
            return std::make_unique<SocketCanDriver>();
        case DriverType::SERIAL:
            return std::make_unique<SerialDriver>();
        case DriverType::MOCK:
            return std::make_unique<MockDriver>();
        default:
            return nullptr;
    }
}

std::unique_ptr<ICommunicationDriver> CommunicationDriverFactory::createDriver(const std::string& type_name) {
    std::string lower_type = type_name;
    std::transform(lower_type.begin(), lower_type.end(), lower_type.begin(), ::tolower);
    
    if (lower_type == "socket_can" || lower_type == "socketcan" || lower_type == "can") {
        return createDriver(DriverType::SOCKET_CAN);
    } else if (lower_type == "serial" || lower_type == "uart" || lower_type == "rs485") {
        return createDriver(DriverType::SERIAL);
    } else if (lower_type == "mock" || lower_type == "test") {
        return createDriver(DriverType::MOCK);
    } else {
        return nullptr;
    }
}

} // namespace driver
} // namespace sl_pmu
