# SL PMU (Power Management Unit)

A layered architecture ROS2 package for motor control and power management, refactored from the original `sl_vcu_all` package.

## Architecture Overview

The package implements a clean 5-layer architecture:

1. **Layer 1: ROS Node Layer** (`src/ros_node/`) - ROS-specific functionality
2. **Layer 2: Application Layer** (`src/application/`) - Business logic without ROS dependencies
3. **Layer 3: <PERSON>ce Layer** (`src/device/`) - Hardware device abstractions
4. **Layer 4: Protocol Layer** (`src/protocol/`) - Communication protocols (CANopen SDO, Modbus RTU)
5. **Layer 5: Driver Layer** (`src/driver/`) - Low-level communication (SocketCAN, Serial)

## Key Features

### Removed from Original Architecture
- ❌ LED control service and related code
- ❌ CAN TX/RX topic publishers/subscribers (uses direct SocketCAN only)
- ❌ `zl_` prefix from device layer classes
- ❌ `vcu_` prefix from application and ROS node classes

### Enhanced Features
- ✅ **Proper Response Waiting**: Sync and async methods wait for actual protocol responses, not just send confirmation
- ✅ **libmodbus Integration**: Modbus RTU protocol uses libmodbus library for robust communication
- ✅ **Explicit Write Size**: CANopen protocol explicitly sets write size based on data value
- ✅ **Direct SocketCAN**: No ROS topic overhead for CAN communication
- ✅ **Protocol Agnostic**: Same device layer works with both CANopen and Modbus protocols

## Supported Protocols

### CANopen SDO
- Uses self-defined implementation
- Explicit write size determination (1, 2, or 4 bytes)
- Proper request/response matching with timeouts
- Direct SocketCAN communication

### Modbus RTU
- Uses libmodbus library for robust communication
- Serial communication over RS485/RS232
- Register mapping compatible with ZL motor controllers
- Automatic CRC calculation and validation

## Device Support

### Motor Controller
- Supports both CANopen and Modbus RTU protocols
- Motor enable/disable sequences
- Speed control with RPM conversion
- Position, current, and temperature monitoring
- GPIO status and alarm handling
- Emergency stop and safety constraints

### Power Controller
- Power button light control
- Main power control
- Power-off sequence management

## Configuration

### CANopen over SocketCAN Example
```yaml
pmu_ros_node:
  ros__parameters:
    driver_type: "socket_can"
    driver_config: "can0"
    protocol_type: "canopen_sdo"
    protocol_node_id: 1
```

### Modbus RTU over Serial Example
```yaml
pmu_ros_node:
  ros__parameters:
    protocol_type: "modbus_rtu"
    modbus_device: "/dev/ttyUSB0"
    modbus_baud_rate: 9600
    modbus_parity: "N"
    modbus_data_bits: 8
    modbus_stop_bits: 1
    modbus_slave_address: 1
```

## Building

```bash
# Install dependencies
sudo apt-get install libmodbus-dev

# Build the package
cd /path/to/your/workspace
colcon build --packages-select sl_pmu
```

## Running

### Basic Launch
```bash
ros2 launch sl_pmu pmu.launch.py
```

### CANopen Configuration
```bash
ros2 launch sl_pmu pmu.launch.py \
  protocol_type:=canopen_sdo \
  driver_config:=can0 \
  protocol_node_id:=1
```

### Modbus RTU Configuration
```bash
ros2 launch sl_pmu pmu.launch.py \
  protocol_type:=modbus_rtu \
  modbus_device:=/dev/ttyUSB0 \
  modbus_baud_rate:=9600 \
  modbus_slave_address:=1
```

## Topics

### Published Topics
- `/odom` (nav_msgs/Odometry) - Robot odometry
- `/motor_info` (sl_vcu_all/MotorInfo) - Detailed motor information
- `/motor_state` (sl_vcu_all/MotorState) - Motor state and alarms
- `/power_off` (std_msgs/Bool) - Power off request status
- `/joint_state` (sensor_msgs/JointState) - Wheel joint states

### Subscribed Topics
- `/cmd_vel` (geometry_msgs/Twist) - Velocity commands
- `/bumper_state` (sl_vcu_all/BumperState) - Bumper sensor state
- `/sl_pmu/imu_data_filtered` (sensor_msgs/Imu) - Filtered IMU data
- `/clear_alarm` (std_msgs/Bool) - Alarm clear requests

## Register Mapping

### CANopen SDO Registers
- `0x6040` - Control word
- `0x6060` - Operation mode
- `0x60FF` - Target velocity
- `0x6064` - Position actual value
- `0x606C` - Velocity actual value
- `0x6077` - Torque actual value
- `0x603F` - Error code

### Modbus RTU Registers
- `0x200D` - Operation mode
- `0x200E` - Control register
- `0x2088` - Speed setpoint
- `0x20A4-0x20B0` - Status registers (temperatures, currents, positions, alarms)

## Safety Features

- Emergency stop monitoring via GPIO
- Bumper-based collision avoidance
- Motor current and temperature monitoring
- Automatic alarm detection and reporting
- Timeout-based safety shutdowns

## Dependencies

- ROS2 (tested with Humble)
- libmodbus-dev
- sl_vcu_all (for message definitions)
- Standard ROS2 packages (rclcpp, geometry_msgs, nav_msgs, etc.)

## Migration from sl_vcu_all

This package provides the same external interface as the original `sl_vcu_all` motor controller node but with:
- Cleaner layered architecture
- Better protocol abstraction
- More robust communication handling
- Easier testing and maintenance
- Support for multiple communication protocols

The ROS topics and message formats remain compatible for seamless migration.
