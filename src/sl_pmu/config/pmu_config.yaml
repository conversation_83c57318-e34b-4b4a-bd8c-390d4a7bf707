# PMU (Power Management Unit) Configuration
# This file contains configuration for the new layered PMU architecture

pmu_ros_node:
  ros__parameters:
    # ========================================
    # Layer 5: Driver Layer Configuration
    # ========================================
    driver_type: "socket_can"              # Options: socket_can, serial, mock
    driver_config: "can0"                  # For socket_can: interface name, for serial: device path
    
    # ========================================
    # Layer 4: Protocol Layer Configuration
    # ========================================
    protocol_type: "canopen_sdo"           # Options: canopen_sdo, modbus_rtu, mock
    protocol_node_id: 1                    # Node ID for the protocol (CAN node ID or Modbus slave ID)
    
    # Modbus RTU specific configuration (only used when protocol_type is modbus_rtu)
    modbus_device: "/dev/ttyUSB0"          # Serial device for Modbus RTU
    modbus_baud_rate: 9600                 # Baud rate for Modbus RTU
    modbus_parity: "N"                     # Parity: N (None), E (Even), O (Odd)
    modbus_data_bits: 8                    # Data bits: 7 or 8
    modbus_stop_bits: 1                    # Stop bits: 1 or 2
    modbus_slave_address: 1                # Modbus slave address
    
    # ========================================
    # Layer 3: Device Layer Configuration
    # ========================================
    motor_device_type: "motor"             # Options: motor, mock
    power_device_type: "power"             # Options: power, mock (empty = disabled)
    
    # ========================================
    # Layer 2: Application Layer Configuration
    # ========================================
    
    # Robot physical parameters
    wheel_diameter_left: 0.1388            # Left wheel diameter in meters
    wheel_diameter_right: 0.140            # Right wheel diameter in meters
    wheel_separation: 0.390                # Distance between wheels in meters
    gear_ratio: 1.0                        # Motor gear ratio
    encoder_resolution: 16384.0            # Encoder ticks per revolution
    
    # Control timing parameters
    control_cycle_ms: 20                   # Main control loop cycle time in milliseconds
    status_update_cycle_ms: 1000           # Status update cycle for temperatures and currents
    
    # Timeout parameters
    cmd_vel_timeout_ms: 500                # Command velocity timeout in milliseconds
    bumper_timeout_ms: 5000                # Bumper state timeout in milliseconds
    
    # Safety parameters
    emergency_stop_bit0_trigger_level: 0   # 0 = low level triggers emergency stop, 1 = high level
    
    # Power control parameters
    power_off_timeout_ms: 5000             # Power off timeout in milliseconds
    power_off_publish_period_ms: 100       # Power off topic publish period in milliseconds
    
    # ========================================
    # Layer 1: ROS Node Layer Configuration
    # ========================================
    
    # Frame IDs for TF and odometry
    odom_frame_id: "odom"                  # Odometry frame ID
    base_frame_id: "base_link"             # Base frame ID
    publish_tf: true                       # Whether to publish TF transforms
    
    # Topic names (NO CAN TX/RX topics - direct SocketCAN only)
    cmd_vel_topic: "cmd_vel"               # Command velocity topic
    odom_topic: "odom"                     # Odometry topic
    filtered_odom_topic: ""                # Filtered odometry topic (optional)
    joint_state_topic: "joint_state"      # Joint state topic
    motor_info_topic: "motor_info"         # Motor information topic
    motor_state_topic: "motor_state"       # Motor state topic
    bumper_topic: "bumper_state"           # Bumper state topic
    power_off_topic: "power_off"           # Power off topic
    filtered_imu_topic: "sl_pmu/imu_data_filtered"  # Filtered IMU topic
    
    # Publishing options
    publish_motor_info: false              # Whether to publish motor info messages
    print_status_out: false                # Whether to print status to console
    
    # IMU integration parameters
    imu_time_offset_ms: 6                  # How much milliseconds IMU messages are ahead of odometry
    
    # ========================================
    # Example Configurations for Different Setups
    # ========================================
    
    # Configuration for CANopen over SocketCAN:
    # driver_type: "socket_can"
    # driver_config: "can0"
    # protocol_type: "canopen_sdo"
    # protocol_node_id: 1
    
    # Configuration for Modbus RTU over Serial:
    # driver_type: "serial"  # Not used for Modbus RTU (libmodbus handles serial directly)
    # protocol_type: "modbus_rtu"
    # modbus_device: "/dev/ttyUSB0"
    # modbus_baud_rate: 9600
    # modbus_parity: "N"
    # modbus_data_bits: 8
    # modbus_stop_bits: 1
    # modbus_slave_address: 1
    
    # Configuration for testing with mock devices:
    # driver_type: "mock"
    # protocol_type: "mock"
    # motor_device_type: "mock"
    # power_device_type: "mock"
