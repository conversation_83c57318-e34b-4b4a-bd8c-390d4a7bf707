<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>sl_pmu</name>
  <version>1.0.0</version>
  <description>Power Management Unit (PMU) with layered architecture for motor control, power management, and device communication</description>
  
  <maintainer email="<EMAIL>">Developer</maintainer>
  <license>MIT</license>
  
  <buildtool_depend>ament_cmake</buildtool_depend>
  
  <!-- Core ROS2 dependencies -->
  <depend>rclcpp</depend>
  <depend>std_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_geometry_msgs</depend>
  
  <!-- Custom message dependencies -->
  <depend>sl_vcu_all</depend>
  
  <!-- System dependencies -->
  <depend>libmodbus-dev</depend>
  
  <!-- Build dependencies -->
  <build_depend>rosidl_default_generators</build_depend>
  <exec_depend>rosidl_default_runtime</exec_depend>
  
  <!-- Test dependencies -->
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_cmake_gtest</test_depend>
  
  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
