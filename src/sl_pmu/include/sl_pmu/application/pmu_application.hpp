#ifndef SL_PMU_APPLICATION_PMU_APPLICATION_HPP_
#define SL_PMU_APPLICATION_PMU_APPLICATION_HPP_

#include "sl_pmu/common/types.hpp"
#include "sl_pmu/device/device_interface.hpp"
#include <memory>
#include <functional>
#include <chrono>

namespace sl_pmu {
namespace application {

/**
 * @brief Configuration for the PMU application
 */
struct PmuConfig {
    // Motor control parameters
    common::RobotParameters robot_params;
    std::chrono::milliseconds cmd_vel_timeout{500};
    std::chrono::milliseconds bumper_timeout{5000};
    std::chrono::milliseconds status_update_interval{1000};
    
    // Safety parameters
    int emergency_stop_trigger_level{0};  // 0 = low level triggers emergency stop
    
    // Power control parameters
    std::chrono::milliseconds power_off_timeout{5000};
    std::chrono::milliseconds power_off_publish_interval{100};
    
    PmuConfig() = default;
};

/**
 * @brief Velocity command structure
 */
struct VelocityCommand {
    double linear_x;    // m/s
    double angular_z;   // rad/s
    std::chrono::steady_clock::time_point timestamp;
    
    VelocityCommand(double lin = 0.0, double ang = 0.0) 
        : linear_x(lin), angular_z(ang), timestamp(std::chrono::steady_clock::now()) {}
};

/**
 * @brief Bumper state structure
 */
struct BumperState {
    bool front_bumper_triggered;
    bool back_bumper_triggered;
    std::chrono::steady_clock::time_point timestamp;
    
    BumperState(bool front = false, bool back = false) 
        : front_bumper_triggered(front), back_bumper_triggered(back)
        , timestamp(std::chrono::steady_clock::now()) {}
};

/**
 * @brief Odometry data structure
 */
struct OdometryData {
    double x;           // m
    double y;           // m
    double theta;       // rad
    double linear_vel;  // m/s
    double angular_vel; // rad/s
    std::chrono::steady_clock::time_point timestamp;
    
    OdometryData() : x(0.0), y(0.0), theta(0.0), linear_vel(0.0), angular_vel(0.0)
        , timestamp(std::chrono::steady_clock::now()) {}
};

/**
 * @brief Motor status structure
 */
struct MotorStatus {
    common::MotorPositions positions;
    common::MotorSpeeds speeds;
    common::MotorCurrents currents;
    common::MotorTemperatures temperatures;
    common::GpioStatus gpio_status;
    uint32_t last_alarm;
    bool motors_enabled;
    std::chrono::steady_clock::time_point timestamp;
    
    MotorStatus() : last_alarm(0), motors_enabled(false)
        , timestamp(std::chrono::steady_clock::now()) {}
};

/**
 * @brief Application state enumeration
 */
enum class ApplicationState {
    INITIALIZING,
    READY,
    RUNNING,
    ERROR,
    SHUTDOWN
};

/**
 * @brief Main PMU application class
 * 
 * This class contains the business logic for motor control and power management
 * without any ROS dependencies.
 */
class PmuApplication {
public:
    PmuApplication();
    virtual ~PmuApplication();

    /**
     * @brief Initialize the application
     * @param config Application configuration
     * @param motor_controller Motor controller device
     * @param power_controller Power controller device (optional)
     * @return true if initialization successful, false otherwise
     */
    bool initialize(const PmuConfig& config,
                   std::shared_ptr<device::IMotorController> motor_controller,
                   std::shared_ptr<device::IPowerController> power_controller = nullptr);

    /**
     * @brief Shutdown the application
     */
    void shutdown();

    /**
     * @brief Get current application state
     * @return Current state
     */
    ApplicationState getState() const;

    /**
     * @brief Process velocity command
     * @param cmd Velocity command to process
     */
    void processVelocityCommand(const VelocityCommand& cmd);

    /**
     * @brief Process bumper state
     * @param state Bumper state to process
     */
    void processBumperState(const BumperState& state);

    /**
     * @brief Request alarm clear
     */
    void requestAlarmClear();

    /**
     * @brief Update application (should be called periodically)
     * This method handles timeouts, state machine updates, and periodic tasks
     */
    void update();

    /**
     * @brief Get current odometry data
     * @return Current odometry data
     */
    OdometryData getOdometryData() const;

    /**
     * @brief Get current motor status
     * @return Current motor status
     */
    MotorStatus getMotorStatus() const;

    /**
     * @brief Get current power status
     * @return Current power status
     */
    common::PowerStatus getPowerStatus() const;

    /**
     * @brief Set status update callback
     * @param callback Callback to call when status is updated
     */
    void setStatusUpdateCallback(std::function<void(const MotorStatus&)> callback);

    /**
     * @brief Set odometry update callback
     * @param callback Callback to call when odometry is updated
     */
    void setOdometryUpdateCallback(std::function<void(const OdometryData&)> callback);

    /**
     * @brief Set power status update callback
     * @param callback Callback to call when power status is updated
     */
    void setPowerStatusUpdateCallback(std::function<void(const common::PowerStatus&)> callback);

    /**
     * @brief Set error callback
     * @param callback Callback to call when an error occurs
     */
    void setErrorCallback(std::function<void(const std::string&)> callback);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl_;
};

} // namespace application
} // namespace sl_pmu

#endif // SL_PMU_APPLICATION_PMU_APPLICATION_HPP_
