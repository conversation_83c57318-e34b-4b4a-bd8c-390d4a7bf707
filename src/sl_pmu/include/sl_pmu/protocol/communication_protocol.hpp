#ifndef SL_PMU_PROTOCOL_COMMUNICATION_PROTOCOL_HPP_
#define SL_PMU_PROTOCOL_COMMUNICATION_PROTOCOL_HPP_

#include "sl_pmu/common/types.hpp"
#include "sl_pmu/driver/communication_driver.hpp"
#include <memory>
#include <functional>

namespace sl_pmu {
namespace protocol {

/**
 * @brief Abstract base class for communication protocols
 * 
 * This layer implements specific communication protocols like CANopen SDO
 * and Modbus RTU, providing a unified interface for device communication.
 * All methods wait for actual response, not just send confirmation.
 */
class ICommunicationProtocol {
public:
    virtual ~ICommunicationProtocol() = default;

    /**
     * @brief Initialize the protocol with a communication driver
     * @param driver The communication driver to use
     * @return true if initialization successful, false otherwise
     */
    virtual bool initialize(std::shared_ptr<driver::ICommunicationDriver> driver) = 0;

    /**
     * @brief Shutdown the protocol
     */
    virtual void shutdown() = 0;

    /**
     * @brief Check if the protocol is ready
     * @return true if ready, false otherwise
     */
    virtual bool isReady() const = 0;

    /**
     * @brief Read a register/parameter synchronously and wait for response
     * @param address Register address
     * @param sub_address Sub-address (for protocols that support it)
     * @param data Output parameter for read data
     * @return Result of the operation (includes actual response data)
     */
    virtual common::AsyncResult readRegister(uint16_t address, uint8_t sub_address, uint32_t& data) = 0;

    /**
     * @brief Write a register/parameter synchronously and wait for response
     * @param address Register address
     * @param sub_address Sub-address (for protocols that support it)
     * @param data Data to write
     * @return Result of the operation (confirms write was acknowledged)
     */
    virtual common::AsyncResult writeRegister(uint16_t address, uint8_t sub_address, uint32_t data) = 0;

    /**
     * @brief Read a register/parameter asynchronously and wait for response
     * @param address Register address
     * @param sub_address Sub-address (for protocols that support it)
     * @param callback Callback to call when response is received
     * @param timeout_config Timeout configuration
     */
    virtual void readRegisterAsync(uint16_t address, uint8_t sub_address,
                                  common::AsyncCallback callback,
                                  const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Write a register/parameter asynchronously and wait for response
     * @param address Register address
     * @param sub_address Sub-address (for protocols that support it)
     * @param data Data to write
     * @param callback Callback to call when response is received
     * @param timeout_config Timeout configuration
     */
    virtual void writeRegisterAsync(uint16_t address, uint8_t sub_address, uint32_t data,
                                   common::AsyncCallback callback,
                                   const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Get protocol type identifier
     * @return String identifying the protocol type
     */
    virtual std::string getProtocolType() const = 0;

    /**
     * @brief Set device node ID (for protocols that support multiple nodes)
     * @param node_id The node ID to communicate with
     */
    virtual void setNodeId(uint8_t node_id) = 0;

    /**
     * @brief Get current device node ID
     * @return Current node ID
     */
    virtual uint8_t getNodeId() const = 0;
};

/**
 * @brief CANopen SDO protocol implementation with explicit write size
 */
class CanopenSdoProtocol : public ICommunicationProtocol {
public:
    CanopenSdoProtocol();
    virtual ~CanopenSdoProtocol();

    // ICommunicationProtocol interface
    bool initialize(std::shared_ptr<driver::ICommunicationDriver> driver) override;
    void shutdown() override;
    bool isReady() const override;
    common::AsyncResult readRegister(uint16_t address, uint8_t sub_address, uint32_t& data) override;
    common::AsyncResult writeRegister(uint16_t address, uint8_t sub_address, uint32_t data) override;
    void readRegisterAsync(uint16_t address, uint8_t sub_address,
                          common::AsyncCallback callback,
                          const common::TimeoutConfig& timeout_config = {}) override;
    void writeRegisterAsync(uint16_t address, uint8_t sub_address, uint32_t data,
                           common::AsyncCallback callback,
                           const common::TimeoutConfig& timeout_config = {}) override;
    std::string getProtocolType() const override;
    void setNodeId(uint8_t node_id) override;
    uint8_t getNodeId() const override;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl_;
};

/**
 * @brief Modbus RTU protocol implementation using libmodbus
 */
class ModbusRtuProtocol : public ICommunicationProtocol {
public:
    ModbusRtuProtocol();
    virtual ~ModbusRtuProtocol();

    // ICommunicationProtocol interface
    bool initialize(std::shared_ptr<driver::ICommunicationDriver> driver) override;
    void shutdown() override;
    bool isReady() const override;
    common::AsyncResult readRegister(uint16_t address, uint8_t sub_address, uint32_t& data) override;
    common::AsyncResult writeRegister(uint16_t address, uint8_t sub_address, uint32_t data) override;
    void readRegisterAsync(uint16_t address, uint8_t sub_address,
                          common::AsyncCallback callback,
                          const common::TimeoutConfig& timeout_config = {}) override;
    void writeRegisterAsync(uint16_t address, uint8_t sub_address, uint32_t data,
                           common::AsyncCallback callback,
                           const common::TimeoutConfig& timeout_config = {}) override;
    std::string getProtocolType() const override;
    void setNodeId(uint8_t node_id) override;
    uint8_t getNodeId() const override;

    /**
     * @brief Initialize Modbus RTU with serial parameters
     * @param device Serial device path (e.g., "/dev/ttyUSB0")
     * @param baud_rate Baud rate
     * @param parity Parity ('N', 'E', 'O')
     * @param data_bits Data bits (7 or 8)
     * @param stop_bits Stop bits (1 or 2)
     * @param slave_address Modbus slave address
     * @return true if initialization successful
     */
    bool initializeModbus(const std::string& device, int baud_rate, char parity, 
                         int data_bits, int stop_bits, int slave_address);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl_;
};

/**
 * @brief Factory for creating communication protocols
 */
class CommunicationProtocolFactory {
public:
    enum class ProtocolType {
        CANOPEN_SDO,
        MODBUS_RTU,
        MOCK  // For testing
    };

    static std::unique_ptr<ICommunicationProtocol> createProtocol(ProtocolType type);
    static std::unique_ptr<ICommunicationProtocol> createProtocol(const std::string& type_name);
};

} // namespace protocol
} // namespace sl_pmu

#endif // SL_PMU_PROTOCOL_COMMUNICATION_PROTOCOL_HPP_
