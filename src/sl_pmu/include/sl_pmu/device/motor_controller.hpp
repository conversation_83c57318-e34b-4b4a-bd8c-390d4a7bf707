#ifndef SL_PMU_DEVICE_MOTOR_CONTROLLER_HPP_
#define SL_PMU_DEVICE_MOTOR_CONTROLLER_HPP_

#include "sl_pmu/device/device_interface.hpp"

namespace sl_pmu {
namespace device {

// Motor controller constants for different protocols
namespace motor_constants {
    // CANopen SDO indices and commands
    namespace canopen {
        // Control commands
        constexpr uint16_t INDEX_CTRL = 0x6040;
        constexpr uint16_t CTRL_RELEASE = 0x06;
        constexpr uint16_t CTRL_ENABLE = 0x07;
        constexpr uint16_t CTRL_SPEED_ENABLE = 0x0F;
        constexpr uint16_t CTRL_ALARM_CLEAR = 0x80;

        // Run modes
        constexpr uint16_t INDEX_RUN_MODE = 0x6060;
        constexpr uint8_t RUN_MODE_POSITION = 0x01;
        constexpr uint8_t RUN_MODE_SPEED = 0x03;
        constexpr uint8_t RUN_MODE_TORQUE = 0x04;

        // Motor parameters
        constexpr uint16_t INDEX_DEST_SPEED = 0x60FF;
        constexpr uint16_t INDEX_POS = 0x6064;
        constexpr uint16_t INDEX_SPEED = 0x606C;
        constexpr uint16_t INDEX_ACC_SPEED_TIME = 0x6083;
        constexpr uint16_t INDEX_DEC_SPEED_TIME = 0x6084;

        // Subindices
        constexpr uint8_t SUBINDEX_DEFAULT = 0x00;
        constexpr uint8_t SUBINDEX_LEFT = 0x01;
        constexpr uint8_t SUBINDEX_RIGHT = 0x02;
        constexpr uint8_t SUBINDEX_L_R = 0x03;

        // Current feedback
        constexpr uint16_t INDEX_CURRENT_FB = 0x6077;
        constexpr uint8_t SUBINDEX_CURRENT_FB_LEFT = 0x01;
        constexpr uint8_t SUBINDEX_CURRENT_FB_RIGHT = 0x02;

        // Temperature
        constexpr uint16_t INDEX_TEMPERATURE = 0x2032;
        constexpr uint8_t SUBINDEX_TEMPERATURE_LEFT = 0x01;
        constexpr uint8_t SUBINDEX_TEMPERATURE_RIGHT = 0x02;
        constexpr uint8_t SUBINDEX_TEMPERATURE_DRIVER = 0x03;

        // Alarm
        constexpr uint16_t INDEX_LAST_ALARM = 0x603F;
        constexpr uint8_t SUBINDEX_LAST_ALARM = 0x00;

        // GPIO
        constexpr uint16_t INDEX_DIN_STATUS = 0x2003;
        constexpr uint16_t INDEX_DOUT_CONTROL = 0x2030;
        constexpr uint8_t SUBINDEX_DOUT_CONTROL = 0x04;
    }

    // Modbus register addresses (based on ZL motor documentation)
    namespace modbus {
        // Mode register
        constexpr uint16_t REG_MODE = 0x200D;
        constexpr uint16_t MODE_SPEED = 0x0003;

        // Control registers
        constexpr uint16_t REG_CONTROL = 0x200E;
        constexpr uint16_t CTRL_DISABLE = 0x0007;
        constexpr uint16_t CTRL_ENABLE = 0x0008;
        constexpr uint16_t CTRL_CLEAR_ALARM = 0x0006;

        constexpr uint16_t REG_SPEED_SET = 0x2088;

        // Status registers (read-only)
        constexpr uint16_t REG_GPIO_STATUS = 0x2003;
        constexpr uint16_t REG_MOTOR_TEMPS = 0x20A4;
        constexpr uint16_t REG_LEFT_ALARM = 0x20A5;
        constexpr uint16_t REG_RIGHT_ALARM = 0x20A6;
        constexpr uint16_t REG_LEFT_POS = 0x20A7;      // 2 registers
        constexpr uint16_t REG_RIGHT_POS = 0x20A9;     // 2 registers
        constexpr uint16_t REG_LEFT_SPEED_FB = 0x20AB;
        constexpr uint16_t REG_RIGHT_SPEED_FB = 0x20AC;
        constexpr uint16_t REG_LEFT_CURRENT = 0x20AD;
        constexpr uint16_t REG_RIGHT_CURRENT = 0x20AE;
        constexpr uint16_t REG_DRIVER_TEMP = 0x20B0;
    }

    // GPIO bit definitions (common for both protocols)
    constexpr uint32_t GPIO_I_EMERGENCY_BIT0 = 0x01;
    constexpr uint32_t GPIO_I_EMERGENCY_BIT1 = 0x02;
    constexpr uint32_t GPIO_I_BRAKE_RELEASE = 0x04;
    constexpr uint32_t GPIO_I_POWER_OFF_BUTTON = 0x08;
    constexpr uint32_t GPIO_O_POWER_OFF_BUTTON_LIGHT = 0x0040;
    constexpr uint32_t GPIO_O_POWER_CONTROL = 0x0100;
}

/**
 * @brief Motor Controller implementation for CANopen and Modbus protocols
 * 
 * This class implements the IMotorController interface and can work with
 * both CANopen SDO and Modbus RTU protocols.
 */
class MotorController : public IMotorController {
public:
    MotorController();
    virtual ~MotorController();

    // IMotorController interface
    bool initialize(std::shared_ptr<protocol::ICommunicationProtocol> protocol,
                   const common::RobotParameters& params) override;
    void shutdown() override;
    bool isReady() const override;
    void enableMotors(common::AsyncCallback callback,
                     const common::TimeoutConfig& timeout_config = {}) override;
    void disableMotors(common::AsyncCallback callback,
                      const common::TimeoutConfig& timeout_config = {}) override;
    void setMotorSpeeds(const common::MotorSpeeds& speeds,
                       common::AsyncCallback callback,
                       const common::TimeoutConfig& timeout_config = {}) override;
    void readMotorPositions(std::function<void(const common::AsyncResult&, const common::MotorPositions&)> callback,
                           const common::TimeoutConfig& timeout_config = {}) override;
    void readMotorSpeeds(std::function<void(const common::AsyncResult&, const common::MotorSpeeds&)> callback,
                        const common::TimeoutConfig& timeout_config = {}) override;
    void readMotorCurrents(std::function<void(const common::AsyncResult&, const common::MotorCurrents&)> callback,
                          const common::TimeoutConfig& timeout_config = {}) override;
    void readMotorTemperatures(std::function<void(const common::AsyncResult&, const common::MotorTemperatures&)> callback,
                              const common::TimeoutConfig& timeout_config = {}) override;
    void readGpioStatus(std::function<void(const common::AsyncResult&, const common::GpioStatus&)> callback,
                       const common::TimeoutConfig& timeout_config = {}) override;
    void readLastAlarm(std::function<void(const common::AsyncResult&, uint32_t)> callback,
                      const common::TimeoutConfig& timeout_config = {}) override;
    void clearAlarm(common::AsyncCallback callback,
                   const common::TimeoutConfig& timeout_config = {}) override;
    std::string getDeviceType() const override;
    bool areMotorsEnabled() const override;

    /**
     * @brief Set emergency stop trigger level
     * @param level 0 = low level triggers emergency stop, 1 = high level triggers emergency stop
     */
    void setEmergencyStopTriggerLevel(int level);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl_;
};

/**
 * @brief Power Controller implementation
 * 
 * This class implements power control functionality.
 */
class PowerController : public IPowerController {
public:
    PowerController();
    virtual ~PowerController();

    // IPowerController interface
    bool initialize(std::shared_ptr<protocol::ICommunicationProtocol> protocol) override;
    void shutdown() override;
    bool isReady() const override;
    void controlPowerButtonLight(bool enable,
                                common::AsyncCallback callback,
                                const common::TimeoutConfig& timeout_config = {}) override;
    void controlMainPower(bool enable,
                         common::AsyncCallback callback,
                         const common::TimeoutConfig& timeout_config = {}) override;
    std::string getDeviceType() const override;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl_;
};

/**
 * @brief Factory for creating devices
 */
class DeviceFactory {
public:
    static std::unique_ptr<IMotorController> createMotorController();
    static std::unique_ptr<IPowerController> createPowerController();
};

} // namespace device
} // namespace sl_pmu

#endif // SL_PMU_DEVICE_MOTOR_CONTROLLER_HPP_
